import axios from 'axios';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { DeclarationAttributes, Element, js2xml, Options, xml2js } from 'xml-js';
import { Buffer } from 'buffer';
import { extractSymbolId } from '../jsonHelper/check';
import * as katex from 'katex';
import { OpenAIService } from './OpenAI';
import * as fuzzball from 'fuzzball';

interface XmlJsStructure {
  declaration?: {
    attributes?: DeclarationAttributes;
  };
  elements?: Element[];
}

interface InternalDocxStructure {
  document: XmlJsStructure;
  styles: XmlJsStructure | null;
  relationships: XmlJsStructure | null;
  comments: XmlJsStructure | null;
  zip: JSZip;
}

interface Comment {
  id: string;
  author: string;
  date: string;
  initials?: string;
  text: string;
  status?: string;
}

/**
 * 定义内容部分的类型
 */
type ContentPart =
  | { type: 'text', content: string }
  | { type: 'formula', content: string, formulaElement?: Element }
  | { type: 'table', content: string, tableStructure: any }
  | { type: 'html', content: string, htmlElement?: Element };

/**
 * Word文档注释管理类 (TypeScript Version)
 */
class WordAnnotationManager {
  // 文档URL
  private url: string;
  private logger: any;
  // 文档对象
  private docxObj: InternalDocxStructure | null = null;
  // 跟踪文档是否被修改
  private isModified = true;

  private readonly parseOptions: Options.XML2JS = {
    compact: false,
    ignoreComment: false,
    ignoreDeclaration: false,
    ignoreInstruction: false,
    ignoreAttributes: false,
    ignoreDoctype: false,
    nativeType: false,
    addParent: true,
    elementsKey: 'elements',
    attributesKey: 'attributes',
    textKey: 'text',
    typeKey: 'type',
    nameKey: 'name',
  };

  private readonly buildOptions: Options.JS2XML = {
    compact: false,
    spaces: 2,
    fullTagEmptyElement: true,
    elementsKey: 'elements',
    attributesKey: 'attributes',
    textKey: 'text',
    typeKey: 'type',
    nameKey: 'name',
  };

  // 注释ID计数器
  private commentIdCounter = 0;

  /**
   * 构造函数
   * @param url - Word文档的URL
   * @param logger
   * @param customPrefix
   */
  constructor(url: string, logger: any = console, customPrefix = '') {
    this.url = url;
    this.logger = this.createPrefixedLogger(logger, customPrefix);
  }

  private createPrefixedLogger(originalLogger: any, prefix: string) {
    const formatAndCall = (method, ...args: any[]) => {
      const message = args.map((arg) => {
        if (typeof arg === 'object' && arg !== null) {
          try {
            return JSON.stringify(arg);
          } catch {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');

      const prefixedMessage = prefix + message;

      if (typeof originalLogger[method] === 'function') {
        (originalLogger[method] as any)(prefixedMessage);
      }
    };
    return { info: (...args) => formatAndCall('info', ...args) };
  }

  /**
   * 在XML解析前，对空格内容进行特殊标记
   * @param xmlContent XML内容字符串
   * @returns 处理后的XML内容
   */
  private markSpaces(xmlContent: string): string {
    // 使用正则表达式找到所有w:t标签内的内容（支持任意属性）
    return xmlContent.replace(/<(w:t(?:\s[^>]*)?)>([\s\S]*?)<\/w:t>/g, (match, openTag, content) => {
      // 如果内容只有空格，进行特殊处理
      if (/^[\s\u3000]*$/.test(content)) {
        // 替换不同类型的空格为特殊标记
        const markedContent = content
          .replace(/ /g, '§SPACE§')
          .replace(/\t/g, '§TAB§')
          .replace(/\u3000/g, '§FULLSPACE§')
          .replace(/\u00A0/g, '§NBSP§');

        return `<${openTag}>${markedContent}</w:t>`;
      }
      return match;
    });
  }

  /**
   * 在XML生成前，将标记的空格恢复为原始空格
   * @param xmlContent XML内容字符串
   * @returns 处理后的XML内容
   */
  private restoreSpaces(xmlContent: string): string {
    // 使用正则表达式找到所有w:t标签内的内容（支持任意属性）
    return xmlContent.replace(/<(w:t(?:\s[^>]*)?)>([\s\S]*?)<\/w:t>/g, (_match, openTag, content) => {
      // 替换特殊标记为相应的空格类型
      const restoredContent = content
        .replace(/§SPACE§/g, ' ')
        .replace(/§TAB§/g, '\t')
        .replace(/§FULLSPACE§/g, '\u3000')
        .replace(/§NBSP§/g, '\u00A0');

      return `<${openTag}>${restoredContent}</w:t>`;
    });
  }

  /**
   * 从URL获取内容
   * @param url 获取内容的URL
   * @returns 返回一个Promise，解析为ArrayBuffer格式的内容
   */
  private async fetchContentFromUrl(url: string): Promise<ArrayBuffer> {
    try {
      // 对URL进行解析，只对pathname部分进行编码处理
      const parsedUrl = new URL(url);
      parsedUrl.pathname = parsedUrl.pathname.split('/')
        .map((segment) => encodeURIComponent(decodeURIComponent(segment)))
        .join('/');

      const encodedUrl = parsedUrl.toString();

      const response = await axios.get(encodedUrl, { responseType: 'arraybuffer' });
      // Axios在Node.js中可能直接返回Buffer，确保返回ArrayBuffer
      if (Buffer.isBuffer(response.data)) {
        return response.data.buffer.slice(response.data.byteOffset, response.data.byteOffset + response.data.byteLength);
      }

      return response.data;
    } catch (error) {
      this.logger.info(`从URL获取文档时出错: ${url}`, error);
      throw new Error(`从URL获取文档失败: ${url}`);
    }
  }

  /**
   * 加载Word文档 (从URL)
   * @returns {Promise<void>}
   */
  public async loadDocument(): Promise<void> {
    try {
      // 从URL获取DOCX内容
      const content: ArrayBuffer = await this.fetchContentFromUrl(this.url);
      // 使用JSZip解压DOCX文件
      const zip = new JSZip();
      const docx = await zip.loadAsync(content);

      /**
       * 辅助函数，用于安全读取和解析XML
       */
      const readAndParseXml = async(fileName: string): Promise<XmlJsStructure | null> => {
        const file = docx.file(fileName);
        if (!file) return null;
        const xmlText = await file.async('text');
        // 在解析前标记空格
        const markedXmlText = this.markSpaces(xmlText);
        return xml2js(markedXmlText, this.parseOptions) as XmlJsStructure;
      };

      const documentObj = await readAndParseXml('word/document.xml');
      if (!documentObj) {
        throw new Error('DOCX文件中未找到word/document.xml。');
      }
      const stylesObj = await readAndParseXml('word/styles.xml');
      const relsObj = await readAndParseXml('word/_rels/document.xml.rels');
      const commentsObj = await readAndParseXml('word/comments.xml');

      if (commentsObj) {
        this.initializeCommentIdCounter(commentsObj);
      } else {
        this.commentIdCounter = 0;
      }

      this.docxObj = {
        document: documentObj,
        styles: stylesObj,
        relationships: relsObj,
        comments: commentsObj,
        zip: docx,
      };

      // 确保Content_Types中包含comments.xml的内容类型
      await this.ensureContentTypesForComments();

      this.isModified = false;
      // this.logger.info('文档加载成功 from URL:', this.url);

    } catch (error) {
      this.logger.info('加载文档时出错:', error);
      this.docxObj = null;
      throw error;
    }
  }

  /**
   * 确保[Content_Types].xml中包含comments.xml的内容类型声明
   * @private
   */
  private async ensureContentTypesForComments(): Promise<void> {
    if (!this.docxObj) return;

    try {
      // 获取[Content_Types].xml文件
      const contentTypesFile = this.docxObj.zip.file('[Content_Types].xml');
      if (!contentTypesFile) {
        this.logger.info('[Content_Types].xml文件不存在');
        return;
      }

      // 读取并解析文件内容
      const content = await contentTypesFile.async('text');
      const contentTypesObj = xml2js(content, this.parseOptions) as XmlJsStructure;

      // 查找Types根元素
      const typesRoot = contentTypesObj.elements?.find((el) =>
        el.type === 'element' && el.name === 'Types');

      if (!typesRoot || !typesRoot.elements) {
        this.logger.info('[Content_Types].xml中未找到Types元素');
        return;
      }

      // 检查是否已存在comments.xml的内容类型声明
      const commentsTypeExists = typesRoot.elements.some((el) =>
        el.type === 'element' &&
        el.name === 'Override' &&
        el.attributes &&
        el.attributes.PartName === '/word/comments.xml' &&
        el.attributes.ContentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml'
      );

      if (commentsTypeExists) {
        // 内容类型已存在，无需添加
        return;
      }

      // 创建新的Override元素
      const commentsTypeElement = {
        type: 'element',
        name: 'Override',
        attributes: {
          PartName: '/word/comments.xml',
          ContentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml',
        },
      };

      // 添加到Types元素
      typesRoot.elements.push(commentsTypeElement);

      // 将修改后的内容写回文件
      const updatedContent = js2xml(contentTypesObj, this.buildOptions);
      this.docxObj.zip.file('[Content_Types].xml', updatedContent);

      this.isModified = true;
    } catch (error) {
      this.logger.info('确保comments内容类型时出错:', error);
    }
  }

  /**
   * 保存文档到文件
   * @returns {Promise<Buffer>} 是否保存成功
   */
  public async saveDocument() {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    try {
      // 构建document.xml
      let documentXml = js2xml(this.docxObj.document, this.buildOptions);
      // 在保存前恢复标记的空格
      documentXml = this.restoreSpaces(documentXml);
      this.docxObj.zip.file('word/document.xml', documentXml);

      // 如果有注释，更新comments.xml
      if (this.docxObj.comments) {
        let commentsXml = js2xml(this.docxObj.comments, this.buildOptions);
        // 在保存前恢复标记的空格
        commentsXml = this.restoreSpaces(commentsXml);
        this.docxObj.zip.file('word/comments.xml', commentsXml);
      }

      // 如果有关系文件，更新它
      if (this.docxObj.relationships) {
        const relsXml = js2xml(this.docxObj.relationships, this.buildOptions);
        this.docxObj.zip.file('word/_rels/document.xml.rels', relsXml);
      }

      // 生成新的DOCX文件
      return await this.docxObj.zip.generateAsync({ type: 'nodebuffer' });
    } catch (error) {
      return null;
    }
  }

  /**
   * 确保document.xml.rels中包含指向comments.xml的关系
   * @private
   * @returns {boolean} 是否添加了新的关系
   */
  private ensureCommentsRelationship() {
    if (!this.docxObj || !this.docxObj.relationships) {
      this.logger.info('文档关系对象不存在，无法添加comments关系');
      return false;
    }

    try {
      // 查找Relationships根元素
      const relsRoot = this.docxObj.relationships.elements!.find((el) =>
        el.type === 'element' && el.name === 'Relationships');

      if (!relsRoot || !relsRoot.elements) {
        this.logger.info('关系根元素不存在');
        return false;
      }

      // 检查是否已存在指向comments.xml的关系
      const commentsRelExists = relsRoot.elements.some((el) =>
        el.type === 'element' &&
        el.name === 'Relationship' &&
        el.attributes &&
        el.attributes.Type === 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments' &&
        el.attributes.Target === 'comments.xml'
      );

      if (commentsRelExists) {
        // 关系已存在，无需添加
        return false;
      }

      // 生成新的关系ID
      const rIds = relsRoot.elements
        .filter((el) => el.type === 'element' && el.name === 'Relationship' && el.attributes && el.attributes.Id)
        .map((el) => el.attributes!.Id)
        .filter((id) => (id as string).startsWith('rId'))
        .map((id) => parseInt((id as string).substring(3), 10))
        .filter((num) => !isNaN(num));

      const maxId = rIds.length > 0 ? Math.max(...rIds) : 0;
      const newRId = `rId${maxId + 1}`;

      // 创建新的关系元素
      const commentsRelElement = {
        type: 'element',
        name: 'Relationship',
        attributes: {
          Id: newRId,
          Type: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments',
          Target: 'comments.xml',
        },
      };

      // 添加到关系列表
      relsRoot.elements.push(commentsRelElement);

      // this.logger.info(`已添加指向comments.xml的关系，ID: ${newRId}`);
      return true;
    } catch (error) {
      this.logger.info('添加comments关系时出错:', error);
      return false;
    }
  }

  /**
   * 初始化注释ID计数器
   * @private
   * @param commentsObj - 注释对象 (XmlJsStructure)
   */
  private initializeCommentIdCounter(commentsObj: XmlJsStructure): void {
    this.commentIdCounter = 0;
    if (!commentsObj?.elements) return;

    try {
      const commentsRoot = commentsObj.elements.find((el) => el.type === 'element' && el.name === 'w:comments');

      if (commentsRoot?.elements) {
        let maxId = -1;
        const commentElements = commentsRoot.elements.filter((el) => el.type === 'element' && el.name === 'w:comment');

        for (const comment of commentElements) {
          const idStr = comment.attributes?.['w:id'] as string | undefined;
          if (idStr) {
            const id = parseInt(idStr, 10);
            if (!isNaN(id) && id > maxId) {
              maxId = id;
            }
          }
        }
        this.commentIdCounter = maxId + 1;
      }
    } catch (error) {
      this.logger.info('初始化注释ID计数器时出错:', error);
      this.commentIdCounter = 0;
    }
  }

  /**
   * 获取文档中的所有注释
   * @returns {array} 注释对象数组
   */
  public getAllComments(): Comment[] {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    const comments: Comment[] = [];
    if (!this.docxObj.comments?.elements) {
      // 没有注释部分或元素
      return comments;
    }

    try {
      const commentsRoot = this.docxObj.comments.elements.find((el) => el.type === 'element' && el.name === 'w:comments');
      if (!commentsRoot?.elements) {
        // 没有实际注释
        return comments;
      }

      const commentElements = commentsRoot.elements.filter((el) => el.type === 'element' && el.name === 'w:comment');

      for (const commentElement of commentElements) {
        const attributes = commentElement.attributes || {};
        const comment: Comment = {
          // 使用空值合并运算符设置默认值
          id: attributes['w:id'] as string ?? '',
          author: attributes['w:author'] as string ?? '',
          date: attributes['w:date'] as string ?? '',
          initials: attributes['w:initials'] as string | undefined,
          text: this.extractTextFromCommentElement(commentElement),
        };
        comments.push(comment);
      }
    } catch (error) {
      this.logger.info('获取注释时出错:', error);
      // 可选择返回部分结果或空数组
      return [];
    }

    return comments;
  }

  /**
   * 提取注释元素中的文本
   * @private
   * @param commentElement - 注释XML元素 (Element)
   * @returns {string} 注释文本
   */
  private extractTextFromCommentElement(commentElement: Element): string {
    if (!commentElement?.elements) return '';

    let text = '';
    try {
      const paragraphs = commentElement.elements.filter((el) => el.type === 'element' && el.name === 'w:p');
      for (const p of paragraphs) {
        text += `${this.extractTextFromParagraph(p)}\n`;
      }
      return text.trim();
    } catch (error) {
      this.logger.info('提取注释文本时出错:', error);
      return '';
    }
  }

  /**
   * 提取段落中的文本内容
   * @private
   * @param paragraph - 段落元素 (Element)
   * @returns {string} 段落文本
   */
  private extractTextFromParagraph(paragraph: Element): string {
    if (!paragraph?.elements) return '';

    let text = '';
    try {
      const runs = paragraph.elements.filter((el) => el.type === 'element' && el.name === 'w:r');
      for (const run of runs) {
        if (!run.elements) continue;
        const textElements = run.elements.filter((el) => el.type === 'element' && el.name === 'w:t');
        for (const t of textElements) {
          // 正确处理直接文本节点和嵌套文本节点
          if (t.elements && t.elements.length > 0) {
            const textNodes = t.elements.filter((el): el is Element & {
              type: 'text',
              text: string
            } => el.type === 'text' && typeof el.text === 'string');
            text += textNodes.map((node) => node.text).join('');
          } else if (t.type === 'text' && typeof t.text === 'string') {
            // 这种情况在xml-js非紧凑模式下可能不会发生，但检查一下也好
            text += t.text;
          }
        }
      }
      return text;
    } catch (error) {
      this.logger.info('提取段落文本时出错:', error);
      return '';
    }
  }

  /**
   * 查找指定名称的子元素 (Helper)
   * @private
   */
  private findElement(parent: any, name: string): Element | undefined {
    return parent?.elements?.find((el) => el.type === 'element' && el.name === name);
  }

  // 如果getComments有特定的预期行为，应在此处定义
  /**
   * 生成唯一的注释ID
   * @returns {string} 新的注释ID (字符串格式)
   */
  private generateCommentId(): string {
    const id = this.commentIdCounter.toString();
    this.commentIdCounter = this.commentIdCounter + 1;
    return id;
  }

  /**
   * 生成包含修改的Word文档的ZIP压缩包 Buffer
   * @returns {Promise<Buffer>} 返回包含DOCX内容的Buffer
   */
  public async generateDocxBuffer(): Promise<Buffer> {
    this.isModified = true;
    if (!this.docxObj) {
      throw new Error('文档未加载，无法生成Buffer');
    }

    try {
      /**
       * 1. 如果已修改，更新JSZip对象中的word/document.xml
       */
      if (this.isModified || !this.docxObj.zip.file('word/document.xml')) {
        let documentXml = js2xml(this.docxObj.document, this.buildOptions);
        // 在生成前恢复标记的空格
        documentXml = this.restoreSpaces(documentXml);
        this.docxObj.zip.file('word/document.xml', documentXml);
        // this.logger.info('已在zip对象中更新word/document.xml。');
      }

      /**
       * 2. 如果存在且可能已修改，更新word/comments.xml
       */
      if (this.docxObj.comments && (this.isModified || !this.docxObj.zip.file('word/comments.xml'))) {
        let commentsXml = js2xml(this.docxObj.comments, this.buildOptions);
        // 在生成前恢复标记的空格
        commentsXml = this.restoreSpaces(commentsXml);
        this.docxObj.zip.file('word/comments.xml', commentsXml);
        // 确保[Content_Types].xml中包含comments.xml的内容类型
        await this.ensureContentTypesForComments();
        // this.logger.info('已在zip对象中更新word/comments.xml。');
      }
      /**
       * 注意：如果关系被修改（例如，添加第一个注释），我们也需要更新它们
       */
      if (this.docxObj.relationships) {
        const relsXml = js2xml(this.docxObj.relationships, this.buildOptions);
        this.docxObj.zip.file('word/_rels/document.xml.rels', relsXml);
      }
      /**
       * 3. 生成ZIP缓冲区
       */
      const outputBuffer = await this.docxObj.zip.generateAsync({
        /**
         * Node.js环境使用'nodebuffer'
         */
        type: 'nodebuffer',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        compression: 'DEFLATE',
        compressionOptions: {
          /**
           * 可选：设置压缩级别（1-9）
           */
          level: 9,
        },
      });

      // this.logger.info('DOCX Buffer生成成功。');
      /**
       * 生成后重置修改标志
       */
      this.isModified = false;
      return outputBuffer;
    } catch (error) {
      this.logger.info('生成DOCX Buffer时出错:', error);
      throw error;
    }
  }

  /**
   * 确保[Content_Types].xml中包含comments.xml的内容类型声明
   * @private
   * @returns {boolean} 是否添加了新的内容类型声明
   */
  private async ensureCommentsContentType(): Promise<boolean> {
    if (!this.docxObj) {
      this.logger.info('文档对象不存在，无法添加comments内容类型');
      return false;
    }

    try {
      // 获取[Content_Types].xml文件
      const contentTypesFile = this.docxObj.zip.file('[Content_Types].xml');
      if (!contentTypesFile) {
        this.logger.info('[Content_Types].xml文件不存在');
        return false;
      }

      // 读取并解析文件内容
      return contentTypesFile.async('text').then((content) => {
        const contentTypesObj = xml2js(content, this.parseOptions) as XmlJsStructure;

        // 查找Types根元素
        const typesRoot = contentTypesObj.elements?.find((el) =>
          el.type === 'element' && el.name === 'Types');

        if (!typesRoot || !typesRoot.elements) {
          this.logger.info('[Content_Types].xml中未找到Types元素');
          return false;
        }

        // 检查是否已存在comments.xml的内容类型声明
        const commentsTypeExists = typesRoot.elements.some((el) =>
          el.type === 'element' &&
          el.name === 'Override' &&
          el.attributes &&
          el.attributes.PartName === '/word/comments.xml' &&
          el.attributes.ContentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml'
        );

        if (commentsTypeExists) {
          // 内容类型已存在，无需添加
          return false;
        }

        // 创建新的Override元素
        const commentsTypeElement = {
          type: 'element',
          name: 'Override',
          attributes: {
            PartName: '/word/comments.xml',
            ContentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml',
          },
        };

        // 添加到Types元素
        typesRoot.elements.push(commentsTypeElement);

        // 将修改后的内容写回文件
        const updatedContent = js2xml(contentTypesObj, this.buildOptions);
        this.docxObj?.zip.file('[Content_Types].xml', updatedContent);

        return true;
      }).catch((error) => {
        this.logger.info('处理[Content_Types].xml时出错:', error);
        return false;
      });
    } catch (error) {
      this.logger.info('确保comments内容类型时出错:', error);
      return false;
    }
  }

  /**
   * 确保文档中存在注释部分和相关文件/关系
   * @private
   */
  private ensureCommentsExist(): void {
    if (!this.docxObj) return;

    let commentsModified = false;

    // 确保[Content_Types].xml中包含comments.xml的内容类型声明
    this.ensureCommentsContentType();

    /**
     * 1. 确保word/comments.xml存在
     */
    if (!this.docxObj.comments) {
      this.docxObj.comments = {
        declaration: { attributes: { version: '1.0', encoding: 'UTF-8', standalone: 'yes' } },
        elements: [{
          type: 'element',
          name: 'w:comments',
          attributes: {
            'xmlns:w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
            'xmlns:mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
            'xmlns:o': 'urn:schemas-microsoft-com:office:office',
            'xmlns:r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
            'xmlns:m': 'http://schemas.openxmlformats.org/officeDocument/2006/math',
            'xmlns:v': 'urn:schemas-microsoft-com:vml',
            'xmlns:wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
            'xmlns:w10': 'urn:schemas-microsoft-com:office:word',
            'xmlns:w14': 'http://schemas.microsoft.com/office/word/2010/wordml',
            'xmlns:w15': 'http://schemas.microsoft.com/office/word/2012/wordml',
            'xmlns:w16cid': 'http://schemas.microsoft.com/office/word/2016/wordml/cid',
            'xmlns:w16se': 'http://schemas.microsoft.com/office/word/2015/wordml/symex',
            'xmlns:wp14': 'http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing',
            'mc:Ignorable': 'w14 w15 w16se w16cid wp14',
          },
          elements: [],
        }],
      };
      /**
       * 将新的空注释文件添加到zip对象
       */
      const commentsXmlStr = js2xml(this.docxObj.comments, this.buildOptions);
      this.docxObj.zip.file('word/comments.xml', commentsXmlStr);
      commentsModified = true;
      // this.logger.info('创建了新的word/comments.xml结构。');
    } else {
      /**
       * 确保<w:comments>元素在结构中存在（如果commentsObj已加载但为空/格式错误）
       */
      let commentsRoot = this.findElement(this.docxObj.comments, 'w:comments');
      if (!commentsRoot) {
        commentsRoot = { type: 'element', name: 'w:comments', attributes: {}, elements: [] };
        /**
         * 添加必要的命名空间，类似于上面的创建块
         */
        if (!this.docxObj.comments.elements) this.docxObj.comments.elements = [];
        this.docxObj.comments.elements.push(commentsRoot);
        commentsModified = true;
      }
      /**
       * 确保elements数组存在
       */
      if (!commentsRoot.elements) {
        commentsRoot.elements = [];
        commentsModified = true;
      }
    }

    if (commentsModified) {
      this.isModified = true;
    }
  }

  /**
   * 根据 customId 查找元素
   * @param elementId - 要查找的元素ID (e.g., 😊para123😊 or para123)
   * @returns {ElementInfo | null} 找到的元素信息，或 null
   */
  public findElementById(elementId) {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    if (!elementId) {
      throw new Error('元素ID不能为空');
    }

    try {
      // 找到document元素
      const docElement = (this.docxObj.document.elements as any).find((el) =>
        el.type === 'element' && el.name === 'w:document');

      if (!docElement || !docElement.elements) {
        throw new Error('无法找到文档主体');
      }

      // 找到body元素
      const bodyElement = docElement.elements.find((el) =>
        el.type === 'element' && el.name === 'w:body');

      if (!bodyElement || !bodyElement.elements) {
        throw new Error('无法找到文档内容');
      }

      // 移除😊前缀和后缀，然后判断类型
      const cleanId = elementId.replace(/^😊(.*)😊$/, '$1');

      let elementType = 'unknown';
      // 根据ID前缀判断类型
      if (cleanId.startsWith('para')) {
        elementType = 'paragraph';
      } else if (cleanId.startsWith('math')) {
        elementType = 'math';
      } else if (cleanId.startsWith('tbl')) {
        elementType = 'table';
      } else if (cleanId.startsWith('img')) {
        elementType = 'image';
      } else {
        // 如果没有特定前缀，默认为段落类型（适应纯数字ID）
        elementType = 'paragraph';
      }

      // 排除w:t标签的节点
      const isTextNode = (element) => {
        return element && element.type === 'element' && element.name === 'w:t';
      };

      // 检查元素是否匹配ID
      const hasMatchingId = (element, id, cleanedId) => {
        return element &&
          element.attributes &&
          (element.attributes.symbol === id ||
            element.attributes.customId === id ||
            element.attributes.customId === `😊${cleanedId}😊` ||
            `😊${element.attributes.customId}😊` === id);
      };

      // 递归查找匹配ID的元素
      const findElementWithId = (element, _elementType: any, path = []) => {
        // 检查当前元素是否匹配
        if (!isTextNode(element) && hasMatchingId(element, elementId, cleanId)) {
          // 检查元素类型是否匹配
          if ((_elementType === 'paragraph' && element.name === 'w:p') ||
            (_elementType === 'math' && (element.name === 'm:oMath' || element.name === 'm:oMathPara')) ||
            (_elementType === 'table' && element.name === 'w:tbl') ||
            (_elementType === 'image' && element.name === 'w:drawing')) {
            return {
              element,
              path,
              type: _elementType,
            };
          }
        }
        // 如果当前元素没有子元素，返回null
        if (!element.elements || element.elements.length === 0) {
          return null;
        }

        // 递归检查子元素
        for (let i = 0; i < element.elements.length; i++) {
          const childElement = element.elements[i];

          // 跳过文本节点
          if (isTextNode(childElement)) {
            continue;
          }

          const newPath: any = [...path, { element: childElement, index: i }];
          const result = findElementWithId(childElement, _elementType, newPath);

          if (result) {
            return result;
          }
        }

        return null;
      };

      // 根据元素类型，查找对应元素
      switch (elementType) {
      case 'paragraph': {
        // 在整个文档中递归查找段落
        const result = findElementWithId(bodyElement, 'paragraph');
        if (result) {
          return {
            element: result.element,
            path: result.path,
            type: 'paragraph',
          };
        }
        break;
      }

      case 'math': {
        // 在整个文档中递归查找数学公式
        const result = findElementWithId(bodyElement, 'math');
        if (result) {
          return {
            element: result.element,
            path: result.path,
            type: 'math',
          };
        }
        break;
      }

      case 'table': {
        // 在整个文档中递归查找表格
        const result = findElementWithId(bodyElement, 'table');
        if (result) {
          return {
            element: result.element,
            path: result.path,
            type: 'table',
          };
        }
        break;
      }

      case 'image': {
        // 在整个文档中递归查找图片
        const result = findElementWithId(bodyElement, 'image');
        if (result) {
          return {
            element: result.element,
            path: result.path,
            type: 'image',
          };
        }
        break;
      }
      }

      return null;
    } catch (error) {
      this.logger.info('查找元素时出错:', error);
      return null;
    }
  }

  /**
   * 为已找到的元素添加带样式的批注（内部方法，避免重复查找元素）
   * @param elementInfo - 已找到的元素信息
   * @param errorInfo - 错误信息
   * @param fixInfo - 修改建议，默认为空字符串
   * @param level - 错误级别 (0-3)，0 最严重，3 最不严重，默认为0
   * @param options - 额外选项
   * @param options.author - 注释作者，默认为 '评注者'
   * @param options.initials - 作者缩写，默认为 'PZ'
   * @param options.targetText - 如果指定，则为段落中的特定文字添加批注，而不是整个元素
   * @returns {string | null} 成功添加返回注释ID，否则返回 null
   */
  private async addCommentToElementByElementInfo(
    elementInfo: any,
    errorInfo: string,
    fixInfo = '',
    level = 0,
    options: {
      author?: string;
      initials?: string;
      targetText?: string;
    } = {}
  ): Promise<string | null> {
    const author = options.author || '评注者';
    const initials = options.initials || 'PZ';
    const targetText = options.targetText || '';

    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }
    if (!elementInfo || (!errorInfo && !fixInfo)) {
      this.logger.info('元素信息和错误信息不能为空');
      return null;
    }

    try {
      // 判断是否为段落中的特定文字添加批注
      if (targetText && elementInfo.type === 'paragraph') {
        return this.addCommentToTextInParagraphByElementInfo(
          elementInfo,
          targetText,
          errorInfo,
          fixInfo,
          level,
          author,
          initials
        );
      }

      // 为整个元素添加批注
      // 1. 确保注释部分和关系存在
      this.ensureCommentsExist();

      // 确保document.xml.rels中包含指向comments.xml的关系
      this.ensureCommentsRelationship();

      // 2. 生成新的注释ID
      const commentId = this.generateCommentId();
      const now = new Date().toISOString();

      // 3. 在comments.xml中创建新的注释结构
      const commentParagraphs: Element[] = [];

      // 根据错误级别设置颜色
      let colorHex = '';
      if (level === 1) {
        // 红色 - 严重错误
        colorHex = 'FF0000';
      } else if (level === 2) {
        // 橙色 - 中等错误
        colorHex = 'FFA500';
      } else if (level === 3) {
        // 黄色 - 轻微错误
        colorHex = 'FFCC00';
      } else if (level === 4) {
        // 蓝色 - AI批注
        colorHex = '3366FF';
      }

      // 4. 处理错误信息和修复信息中的公式和表格
      const { contentParts: errorContentParts } =
        await this.processContent(errorInfo, colorHex);

      let fixContentParts: ContentPart[] = [];
      if (fixInfo) {
        const result = await this.processContent(fixInfo);
        fixContentParts = result.contentParts;
      }

      // 创建错误信息段落 - 居中加粗
      const errorInfoParagraph: any = {
        type: 'element',
        name: 'w:p',
        elements: [
          {
            type: 'element',
            name: 'w:pPr',
            elements: [
              {
                type: 'element',
                name: 'w:pStyle',
                attributes: { 'w:val': 'CommentText' },
              },
              {
                type: 'element',
                name: 'w:jc',
                // 居中对齐
                attributes: { 'w:val': 'center' },
              }
            ],
          }
        ],
      };

      // 添加错误信息内容（文本和公式）
      for (const part of errorContentParts) {
        if (part.type === 'text') {
          // 错误信息部分 - 加粗带颜色
          errorInfoParagraph.elements.push({
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:rPr',
                elements: [
                  {
                    // 加粗
                    type: 'element',
                    name: 'w:b',
                    attributes: { 'w:val': 'true' },
                  },
                  // 根据级别添加颜色
                  ...(colorHex ? [{
                    type: 'element',
                    name: 'w:color',
                    attributes: { 'w:val': colorHex },
                  }] : [])
                ],
              },
              {
                type: 'element',
                name: 'w:t',
                elements: [
                  { type: 'text', text: part.content }
                ],
              }
            ],
          });
        } else if (part.type === 'formula' && part.formulaElement) {
          // 为公式创建一个运行，保持错误信息的颜色和样式
          const formulaRun = {
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:rPr',
                elements: [
                  // 可以根据需要添加特定于公式的样式
                  ...(colorHex ? [{
                    type: 'element',
                    name: 'w:color',
                    attributes: { 'w:val': colorHex },
                  }] : [])
                ],
              },
              part.formulaElement
            ],
          };

          errorInfoParagraph.elements.push(formulaRun);
        }
      }

      // 如果有修改建议，添加到相同段落中
      if (fixInfo) {
        // 先添加换行符
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:br',
            }
          ],
        });

        // 添加修改建议内容（文本和公式）
        for (const part of fixContentParts) {
          if (part.type === 'text') {
            // 添加修改建议文本，不加粗
            errorInfoParagraph.elements.push({
              type: 'element',
              name: 'w:r',
              elements: [
                {
                  type: 'element',
                  name: 'w:t',
                  elements: [
                    { type: 'text', text: part.content }
                  ],
                }
              ],
            });
          } else if (part.type === 'formula' && part.formulaElement) {
            // 为公式创建一个运行
            const formulaRun = {
              type: 'element',
              name: 'w:r',
              elements: [
                {
                  type: 'element',
                  name: 'w:rPr',
                  elements: [], // 可以根据需要添加特定于公式的样式
                },
                part.formulaElement
              ],
            };

            errorInfoParagraph.elements.push(formulaRun);
          }
        }
      }

      commentParagraphs.push(errorInfoParagraph);

      // 创建完整的注释元素
      const newCommentElement: Element = {
        type: 'element',
        name: 'w:comment',
        attributes: {
          'w:id': commentId,
          'w:author': author,
          'w:initials': initials,
          'w:date': now,
        },
        elements: commentParagraphs,
      };

      // 5. 将新注释添加到注释对象
      const commentsRoot = this.findElement(this.docxObj.comments, 'w:comments');
      if (!commentsRoot || !commentsRoot.elements) {
        throw new Error('在 ensureCommentsExist 之后找不到或格式错误的w:comments元素');
      }
      commentsRoot.elements.push(newCommentElement);

      // 6. 在主文档中添加注释引用标记
      this.addCommentReferenceToElement(elementInfo, commentId);

      // 标记文档为已修改
      this.isModified = true;
      return commentId;

    } catch (error) {
      this.logger.info('添加评论时出错:', error);
      return null;
    }
  }

  /**
   * 为元素或段落中的文本添加带样式的批注
   * @param elementId - 元素ID或段落ID (带或不带标记)
   * @param errorInfo - 错误信息
   * @param fixInfo - 修改建议，默认为空字符串
   * @param level - 错误级别 (0-3)，0 最严重，3 最不严重，默认为0
   * @param options - 额外选项
   * @param options.author - 注释作者，默认为 '评注者'
   * @param options.initials - 作者缩写，默认为 'PZ'
   * @param options.targetText - 如果指定，则为段落中的特定文字添加批注，而不是整个元素
   * @returns {string | null} 成功添加返回注释ID，否则返回 null
   */
  public async addCommentToElementById(
    elementId: string,
    errorInfo: string,
    fixInfo = '',
    level = 0,
    options: {
      author?: string;
      initials?: string;
      targetText?: string;
    } = {}
  ): Promise<string | null> {
    const author = options.author || '评注者';
    const initials = options.initials || 'PZ';
    const targetText = options.targetText || '';

    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }
    if (!elementId || (!errorInfo && !fixInfo)) {
      this.logger.info('元素ID和错误信息不能为空');
      return null;
    }

    try {
      // 判断是否为段落中的特定文字添加批注
      if (targetText) {
        return this.addCommentToTextInParagraphInternal(
          elementId,
          targetText,
          errorInfo,
          fixInfo,
          level,
          author,
          initials
        );
      }

      // 为整个元素添加批注
      // 1. 查找目标元素
      const elementInfo = this.findElementById(elementId);
      if (!elementInfo) {
        this.logger.info(`未找到ID为 ${elementId} 的元素，无法添加注释`);
        return null;
      }

      // 2. 确保注释部分和关系存在
      this.ensureCommentsExist();

      // 确保document.xml.rels中包含指向comments.xml的关系
      this.ensureCommentsRelationship();

      // 3. 生成新的注释ID
      const commentId = this.generateCommentId();
      const now = new Date().toISOString();

      // 4. 在comments.xml中创建新的注释结构
      const commentParagraphs: Element[] = [];

      // 根据错误级别设置颜色
      let colorHex = '';
      if (level === 1) {
        // 红色 - 严重错误
        colorHex = 'FF0000';
      } else if (level === 2) {
        // 橙色 - 中等错误
        colorHex = 'FFA500';
      } else if (level === 3) {
        // 黄色 - 轻微错误
        colorHex = 'FFCC00';
      } else if (level === 4) {
        // 蓝色 - AI批注
        colorHex = '3366FF';
      }

      // 5. 处理错误信息和修复信息中的公式和表格
      const { contentParts: errorContentParts } =
        await this.processContent(errorInfo, colorHex);

      let fixContentParts: ContentPart[] = [];
      if (fixInfo) {
        const result = await this.processContent(fixInfo);
        fixContentParts = result.contentParts;
      }

      // 创建错误信息段落 - 居中加粗
      const errorInfoParagraph: any = {
        type: 'element',
        name: 'w:p',
        elements: [
          {
            type: 'element',
            name: 'w:pPr',
            elements: [
              {
                type: 'element',
                name: 'w:pStyle',
                attributes: { 'w:val': 'CommentText' },
              },
              {
                type: 'element',
                name: 'w:jc',
                // 居中对齐
                attributes: { 'w:val': 'center' },
              }
            ],
          }
        ],
      };

      // 添加错误信息内容（文本和公式）
      for (const part of errorContentParts) {
        if (part.type === 'text') {
          // 错误信息部分 - 加粗带颜色
          errorInfoParagraph.elements.push({
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:rPr',
                elements: [
                  {
                    // 加粗
                    type: 'element',
                    name: 'w:b',
                    attributes: { 'w:val': 'true' },
                  },
                  // 根据级别添加颜色
                  ...(colorHex ? [{
                    type: 'element',
                    name: 'w:color',
                    attributes: { 'w:val': colorHex },
                  }] : [])
                ],
              },
              {
                type: 'element',
                name: 'w:t',
                elements: [
                  { type: 'text', text: part.content }
                ],
              }
            ],
          });
        } else if (part.type === 'formula' && part.formulaElement) {
          // 为公式创建一个运行，保持错误信息的颜色和样式
          const formulaRun = {
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:rPr',
                elements: [
                  // 可以根据需要添加特定于公式的样式
                  ...(colorHex ? [{
                    type: 'element',
                    name: 'w:color',
                    attributes: { 'w:val': colorHex },
                  }] : [])
                ],
              },
              part.formulaElement
            ],
          };

          errorInfoParagraph.elements.push(formulaRun);
        }
      }

      // 如果有修改建议，添加到相同段落中
      if (fixInfo) {
        // 先添加换行符
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:br',
            }
          ],
        });

        // 添加修改建议内容（文本和公式）
        for (const part of fixContentParts) {
          if (part.type === 'text') {
            // 添加修改建议文本，不加粗
            errorInfoParagraph.elements.push({
              type: 'element',
              name: 'w:r',
              elements: [
                {
                  type: 'element',
                  name: 'w:t',
                  elements: [
                    { type: 'text', text: part.content }
                  ],
                }
              ],
            });
          } else if (part.type === 'formula' && part.formulaElement) {
            // 为公式创建一个运行
            const formulaRun = {
              type: 'element',
              name: 'w:r',
              elements: [
                {
                  type: 'element',
                  name: 'w:rPr',
                  elements: [], // 可以根据需要添加特定于公式的样式
                },
                part.formulaElement
              ],
            };

            errorInfoParagraph.elements.push(formulaRun);
          }
        }
      }

      commentParagraphs.push(errorInfoParagraph);

      // 创建完整的注释元素
      const newCommentElement: Element = {
        type: 'element',
        name: 'w:comment',
        attributes: {
          'w:id': commentId,
          'w:author': author,
          'w:initials': initials,
          'w:date': now,
        },
        elements: commentParagraphs,
      };

      // 6. 将新注释添加到注释对象
      const commentsRoot = this.findElement(this.docxObj.comments, 'w:comments');
      if (!commentsRoot || !commentsRoot.elements) {
        throw new Error('在 ensureCommentsExist 之后找不到或格式错误的w:comments元素');
      }
      commentsRoot.elements.push(newCommentElement);

      // 7. 在主文档中添加注释引用标记
      this.addCommentReferenceToElement(elementInfo, commentId);

      // 标记文档为已修改
      this.isModified = true;
      return commentId;

    } catch (error) {
      this.logger.info('添加评论时出错:', error);
      return null;
    }
  }

  /**
   * 为已找到的段落元素中的特定文字添加带样式的批注
   * @private
   */
  private async addCommentToTextInParagraphByElementInfo(
    elementInfo: any,
    targetText: string,
    errorInfo: string,
    fixInfo = '',
    level = 0,
    author = '评注者',
    initials = 'PZ'
  ): Promise<string | null> {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    if (!elementInfo || !elementInfo.element) {
      this.logger.info('元素信息不能为空');
      return null;
    }

    try {
      const paragraph = elementInfo.element;
      if (!paragraph.elements) {
        this.logger.info('段落元素格式不正确');
        return null;
      }

      // 2. 增强的关键词查找逻辑 - 支持跨越多个w:r元素的情况
      const textSearchResult = this.findTextInParagraph(paragraph, targetText);

      if (!textSearchResult.found) {
        this.logger.info(`在段落中未找到文字: "${targetText}"，将批注添加到段落末尾`);
      }

      // 3. 确保注释部分和关系存在
      this.ensureCommentsExist();
      // 确保document.xml.rels中包含指向comments.xml的关系
      this.ensureCommentsRelationship();
      // 4. 生成新的注释ID
      const commentId = this.generateCommentId();
      const now = new Date().toISOString();

      // 4. 在comments.xml中创建新的注释结构
      const commentParagraphs: Element[] = [];

      // 根据错误级别设置颜色
      let colorHex = '';
      if (level === 1) {
        // 红色 - 严重错误
        colorHex = 'FF0000';
      } else if (level === 2) {
        // 橙色 - 中等错误
        colorHex = 'FFA500';
      } else if (level === 3) {
        // 黄色 - 轻微错误
        colorHex = 'FFCC00';
      } else if (level === 4) {
        // 蓝色 - AI批注
        colorHex = '3366FF';
      }

      // 创建错误信息段落 - 居中加粗
      const errorInfoParagraph: any = {
        type: 'element',
        name: 'w:p',
        elements: [
          {
            type: 'element',
            name: 'w:pPr',
            elements: [
              {
                type: 'element',
                name: 'w:pStyle',
                attributes: { 'w:val': 'CommentText' },
              },
              {
                type: 'element',
                name: 'w:jc',
                // 居中对齐
                attributes: { 'w:val': 'center' },
              }
            ],
          }
        ],
      };
      if (errorInfo) {
        // 错误信息部分 - 加粗带颜色
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:rPr',
              elements: [
                {
                  // 加粗
                  type: 'element',
                  name: 'w:b',
                  attributes: { 'w:val': 'true' },
                },
                // 根据级别添加颜色
                ...(colorHex ? [{
                  type: 'element',
                  name: 'w:color',
                  attributes: { 'w:val': colorHex },
                }] : [])
              ],
            },
            {
              type: 'element',
              name: 'w:t',
              elements: [
                { type: 'text', text: errorInfo }
              ],
            }
          ],
        });
      }
      // 如果有修改建议，添加到相同段落中
      if (fixInfo) {
        if (errorInfo) {
          // 如果前面有内容，就先添加换行符
          errorInfoParagraph.elements.push({
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:br',
              }
            ],
          });
        }

        // 再添加修改建议文本，不加粗，不居中
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:t',
              elements: [
                { type: 'text', text: fixInfo }
              ],
            }
          ],
        });
      }

      commentParagraphs.push(errorInfoParagraph);

      // 创建完整的注释元素
      const newCommentElement: Element = {
        type: 'element',
        name: 'w:comment',
        attributes: {
          'w:id': commentId,
          'w:author': author,
          'w:initials': initials,
          'w:date': now,
        },
        elements: commentParagraphs,
      };

      // 5. 将新注释添加到注释对象
      const commentsRoot = this.findElement(this.docxObj.comments, 'w:comments');
      if (!commentsRoot || !commentsRoot.elements) {
        throw new Error('在 ensureCommentsExist 之后找不到或格式错误的w:comments元素');
      }
      commentsRoot.elements.push(newCommentElement);

      // 6. 添加注释到文档中
      if (!textSearchResult.found) {
        // 创建注释标记元素
        const commentStartRun: any = {
          type: 'element',
          name: 'w:commentRangeStart',
          attributes: { 'w:id': commentId },
        };

        const commentEndRun: any = {
          type: 'element',
          name: 'w:commentRangeEnd',
          attributes: { 'w:id': commentId },
        };

        const commentReference: any = {
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:rPr',
              elements: [
                {
                  type: 'element',
                  name: 'w:rStyle',
                  attributes: { 'w:val': 'CommentReference' },
                }
              ],
            },
            {
              type: 'element',
              name: 'w:commentReference',
              attributes: { 'w:id': commentId },
            }
          ],
        };

        // 添加注释标记到段落末尾
        paragraph.elements.push(commentStartRun);
        paragraph.elements.push(commentEndRun);
        paragraph.elements.push(commentReference);
      } else {
        // 7. 使用增强的文本查找结果添加注释标记
        this.addCommentMarkersToFoundText(paragraph, textSearchResult, commentId);
      }

      // 标记文档为已修改
      this.isModified = true;
      return commentId;

    } catch (error) {
      return null;
    }
  }

  /**
   * 内部方法：为段落中的特定文字添加带样式的批注
   * @private
   */
  private async addCommentToTextInParagraphInternal(
    paragraphId: string,
    targetText: string,
    errorInfo: string,
    fixInfo = '',
    level = 0,
    author = '评注者',
    initials = 'PZ'
  ): Promise<string | null> {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    if (!paragraphId) {
      this.logger.info('段落ID、目标文字和错误信息不能为空');
      return null;
    }

    try {
      // 1. 查找目标段落
      const elementInfo = this.findElementById(paragraphId);
      if (!elementInfo || elementInfo.type !== 'paragraph') {
        this.logger.info(`未找到ID为 ${paragraphId} 的段落`);
        return null;
      }

      const paragraph = elementInfo.element;
      if (!paragraph.elements) {
        this.logger.info('段落元素格式不正确');
        return null;
      }

      // 2. 增强的关键词查找逻辑 - 支持跨越多个w:r元素的情况
      const textSearchResult = this.findTextInParagraph(paragraph, targetText);

      if (!textSearchResult.found) {
        this.logger.info(`在段落中未找到文字: "${targetText}"，将批注添加到段落末尾`);
      }

      // 3. 确保注释部分和关系存在
      this.ensureCommentsExist();
      // 确保document.xml.rels中包含指向comments.xml的关系
      this.ensureCommentsRelationship();
      // 4. 生成新的注释ID
      const commentId = this.generateCommentId();
      const now = new Date().toISOString();

      // 4. 在comments.xml中创建新的注释结构
      const commentParagraphs: Element[] = [];

      // 根据错误级别设置颜色
      let colorHex = '';
      if (level === 1) {
        // 红色 - 严重错误
        colorHex = 'FF0000';
      } else if (level === 2) {
        // 橙色 - 中等错误
        colorHex = 'FFA500';
      } else if (level === 3) {
        // 黄色 - 轻微错误
        colorHex = 'FFCC00';
      } else if (level === 4) {
        // 蓝色 - AI批注
        colorHex = '3366FF';
      }

      // 创建错误信息段落 - 居中加粗
      const errorInfoParagraph: any = {
        type: 'element',
        name: 'w:p',
        elements: [
          {
            type: 'element',
            name: 'w:pPr',
            elements: [
              {
                type: 'element',
                name: 'w:pStyle',
                attributes: { 'w:val': 'CommentText' },
              },
              {
                type: 'element',
                name: 'w:jc',
                // 居中对齐
                attributes: { 'w:val': 'center' },
              }
            ],
          }
        ],
      };
      if (errorInfo) {
        // 错误信息部分 - 加粗带颜色
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:rPr',
              elements: [
                {
                  // 加粗
                  type: 'element',
                  name: 'w:b',
                  attributes: { 'w:val': 'true' },
                },
                // 根据级别添加颜色
                ...(colorHex ? [{
                  type: 'element',
                  name: 'w:color',
                  attributes: { 'w:val': colorHex },
                }] : [])
              ],
            },
            {
              type: 'element',
              name: 'w:t',
              elements: [
                { type: 'text', text: errorInfo }
              ],
            }
          ],
        });
      }
      // 如果有修改建议，添加到相同段落中
      if (fixInfo) {
        if (errorInfo) {
        // 如果前面有内容，就先添加换行符
          errorInfoParagraph.elements.push({
            type: 'element',
            name: 'w:r',
            elements: [
              {
                type: 'element',
                name: 'w:br',
              }
            ],
          });
        }

        // 再添加修改建议文本，不加粗，不居中
        errorInfoParagraph.elements.push({
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:t',
              elements: [
                { type: 'text', text: fixInfo }
              ],
            }
          ],
        });
      }

      commentParagraphs.push(errorInfoParagraph);

      // 创建完整的注释元素
      const newCommentElement: Element = {
        type: 'element',
        name: 'w:comment',
        attributes: {
          'w:id': commentId,
          'w:author': author,
          'w:initials': initials,
          'w:date': now,
        },
        elements: commentParagraphs,
      };

      // 5. 将新注释添加到注释对象
      const commentsRoot = this.findElement(this.docxObj.comments, 'w:comments');
      if (!commentsRoot || !commentsRoot.elements) {
        throw new Error('在 ensureCommentsExist 之后找不到或格式错误的w:comments元素');
      }
      commentsRoot.elements.push(newCommentElement);

      // 6. 添加注释到文档中
      if (!textSearchResult.found) {
        // 创建注释标记元素
        const commentStartRun: any = {
          type: 'element',
          name: 'w:commentRangeStart',
          attributes: { 'w:id': commentId },
        };

        const commentEndRun: any = {
          type: 'element',
          name: 'w:commentRangeEnd',
          attributes: { 'w:id': commentId },
        };

        const commentReference: any = {
          type: 'element',
          name: 'w:r',
          elements: [
            {
              type: 'element',
              name: 'w:rPr',
              elements: [
                {
                  type: 'element',
                  name: 'w:rStyle',
                  attributes: { 'w:val': 'CommentReference' },
                }
              ],
            },
            {
              type: 'element',
              name: 'w:commentReference',
              attributes: { 'w:id': commentId },
            }
          ],
        };

        // 添加注释标记到段落末尾
        paragraph.elements.push(commentStartRun);
        paragraph.elements.push(commentEndRun);
        paragraph.elements.push(commentReference);
      } else {
        // 7. 使用增强的文本查找结果添加注释标记
        this.addCommentMarkersToFoundText(paragraph, textSearchResult, commentId);
      }

      // 标记文档为已修改
      this.isModified = true;
      return commentId;

    } catch (error) {
      return null;
    }
  }

  /**
   * 在主文档中的元素上添加注释引用标记 (<w:commentRangeStart>, <w:commentRangeEnd>, <w:commentReference>)
   * @private
   */
  private addCommentReferenceToElement(elementInfo: any, commentId: string) {
    const { element, type, path } = elementInfo;

    // 创建注释引用节点
    const commentReference = {
      type: 'element',
      name: 'w:r',
      elements: [
        {
          type: 'element',
          name: 'w:rPr',
          elements: [
            {
              type: 'element',
              name: 'w:rStyle',
              attributes: { 'w:val': 'CommentReference' },
            }
          ],
        },
        {
          type: 'element',
          name: 'w:commentReference',
          attributes: { 'w:id': commentId },
        }
      ],
    };

    if (type === 'paragraph') {
      // 确保元素有elements数组
      if (!element.elements) {
        element.elements = [];
      }

      // 创建注释标记节点
      const commentStartRun = {
        type: 'element',
        name: 'w:commentRangeStart',
        attributes: { 'w:id': commentId },
      };

      const commentEndRun = {
        type: 'element',
        name: 'w:commentRangeEnd',
        attributes: { 'w:id': commentId },
      };

      // 添加注释标记到元素末尾
      element.elements.push(commentStartRun);
      element.elements.push(commentEndRun);
      element.elements.push(commentReference);
    } else if (type === 'math') {
      // 为数学公式添加注释标记
      // 查找数学公式所在的段落
      let paragraphElement: any = null;

      if (path && path.length > 0) {
        // 查找最近的段落元素
        for (let i = path.length - 1; i >= 0; i--) {
          if (path[i].element.name === 'w:p') {
            paragraphElement = path[i].element;
            break;
          }
        }
      }

      if (paragraphElement) {
        if (!paragraphElement.elements) {
          paragraphElement.elements = [];
        }

        // 在数学公式后面添加注释引用
        const mathIndex = paragraphElement.elements.findIndex((el) => el === element);

        if (mathIndex !== -1) {
          // 创建注释范围结束标记
          const commentRangeEnd = {
            type: 'element',
            name: 'w:commentRangeEnd',
            attributes: { 'w:id': commentId },
          };

          // 在公式前添加注释范围开始标记
          paragraphElement.elements.splice(mathIndex, 0, {
            type: 'element',
            name: 'w:commentRangeStart',
            attributes: { 'w:id': commentId },
          });

          // 在公式后添加注释范围结束标记和注释引用
          paragraphElement.elements.splice(mathIndex + 2, 0, commentRangeEnd, commentReference);
        } else {
          // 如果找不到精确位置，则添加到段落末尾
          paragraphElement.elements.push(commentReference);
        }
      } else {
        // 如果找不到段落，直接添加到元素后面
        if (!element.elements) {
          element.elements = [];
        }
        element.elements.push(commentReference);
      }
    } else if (type === 'table') {
      // 为表格添加注释标记
      if (!element.elements) {
        element.elements = [];
      }
      element.elements.push(commentReference);
    } else if (type === 'image') {
      // 为图片添加注释标记 - 调整为包裹整个w:r元素

      // 找到图片所在的w:r元素
      let runElement: any = null;
      let runParent: any = null;
      let runIndex = -1;

      // 图片通常在 w:r > w:drawing 结构中
      if (path && path.length > 0) {
        // 遍历路径找到w:r元素
        for (let i = path.length - 1; i >= 0; i--) {
          if (path[i].element.name === 'w:r') {
            runElement = path[i].element;
            runIndex = path[i].index;

            // 找到w:r的父元素(通常是w:p)
            if (i > 0) {
              runParent = path[i - 1].element;
            }
            break;
          }
        }
      }

      // 创建注释范围开始和结束标记
      const commentStartRun = {
        type: 'element',
        name: 'w:commentRangeStart',
        attributes: { 'w:id': commentId },
      };

      const commentEndRun = {
        type: 'element',
        name: 'w:commentRangeEnd',
        attributes: { 'w:id': commentId },
      };

      if (runElement && runParent && runParent.elements) {
        // 在找到的w:r元素周围添加注释标记
        runParent.elements.splice(runIndex, 0, commentStartRun);
        runParent.elements.splice(runIndex + 2, 0, commentEndRun);
        runParent.elements.splice(runIndex + 3, 0, commentReference);
      } else {
        // 如果找不到w:r元素，尝试找到包含图片的段落
        let paragraphElement: any = null;

        if (path && path.length > 0) {
          // 查找最近的段落元素
          for (let i = path.length - 1; i >= 0; i--) {
            if (path[i].element.name === 'w:p') {
              paragraphElement = path[i].element;
              break;
            }
          }
        }

        if (paragraphElement && paragraphElement.elements) {
          // 如果找到段落但找不到run，则添加到段落末尾(备选方案)
          paragraphElement.elements.push(commentStartRun);
          paragraphElement.elements.push(commentEndRun);
          paragraphElement.elements.push(commentReference);
        } else {
          // 最后的备选方案：直接添加到图片元素的父元素
          let parentElement: any = null;

          if (path && path.length >= 2) {
            parentElement = path[path.length - 2].element;
          }

          if (parentElement && parentElement.elements) {
            parentElement.elements.push(commentStartRun);
            parentElement.elements.push(commentEndRun);
            parentElement.elements.push(commentReference);
          } else if (element.elements) {
            // 如果都找不到，添加到元素自身
            element.elements.push(commentStartRun);
            element.elements.push(commentEndRun);
            element.elements.push(commentReference);
          }
        }
      }
    }
  }

  /**
   * 删除所有带有customId属性的节点和w:t中包含😊(.*?)😊的文本标记
   * @returns {Promise<object>} 返回删除结果统计
   */
  public removeAllCustomIdsAndMarkers() {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    try {
      const stats: any = {
        removedCustomIds: 0,
        removedTextMarkers: 0,
        removedRunElements: 0,
      };

      // 找到document元素
      const docElement = this.docxObj.document.elements!.find((el) =>
        el.type === 'element' && el.name === 'w:document');

      if (!docElement || !docElement.elements) {
        throw new Error('无法找到文档主体');
      }

      // 找到body元素
      const bodyElement = docElement.elements.find((el) =>
        el.type === 'element' && el.name === 'w:body');

      if (!bodyElement || !bodyElement.elements) {
        throw new Error('无法找到文档内容');
      }

      // 递归处理所有元素
      const processElement = (element: any, isInTable = false) => {
        if (!element) return;

        // 1. 如果元素有customId属性，删除它
        if (element.attributes && element.attributes.customId) {
          delete element.attributes.customId;
          stats.removedCustomIds = (stats.removedCustomIds || 0) + 1;
        }

        // 检查当前元素是否是表格或表格单元格
        const isTable = element.type === 'element' && element.name === 'w:tbl';
        const isTableCell = element.type === 'element' && element.name === 'w:tc';

        // 在表格内部处理时更新标志
        const currentIsInTable = isInTable || isTable || isTableCell;

        // 递归处理子元素，并记录需要删除的元素索引
        if (element.elements && element.elements.length > 0) {
          const elementsToRemove: any[] = [];
          const elementsToModify: { index: number, newText: string }[] = [];

          // 首先找出需要删除或修改的元素
          for (let i = 0; i < element.elements.length; i++) {
            const childElement = element.elements[i];

            // 检查是否是w:r元素并且包含带有😊(.*?)😊标记的w:t元素
            if (childElement.type === 'element' && childElement.name === 'w:r' && childElement.elements) {
              const textElements = childElement.elements.filter((el) =>
                el.type === 'element' && el.name === 'w:t');

              let hasMarker = false;
              let cleanedText = '';
              let originalText = '';

              for (const textEl of textElements) {
                if (textEl.elements) {
                  for (const textNode of textEl.elements) {
                    if (textNode.type === 'text' && textNode.text) {
                      originalText = textNode.text;
                      if (/😊([^😊]+)😊/.test(textNode.text)) {
                        hasMarker = true;
                        // 完全删除😊xxx😊标记及其内容
                        cleanedText = textNode.text.replace(/😊([^😊]+)😊/g, '');
                      }
                      break;
                    }
                  }
                }
                if (hasMarker) break;
              }

              if (hasMarker) {
                // 判断处理方式：
                // 1. 如果在表格内，总是保留元素但修改文本
                // 2. 如果不在表格内，且替换后文本为空，则删除元素
                // 3. 如果不在表格内，但替换后文本不为空，则保留元素但修改文本
                if (currentIsInTable || cleanedText !== '') {
                  // 保留元素但修改文本
                  elementsToModify.push({ index: i, newText: cleanedText });
                  stats.removedTextMarkers = (stats.removedTextMarkers || 0) + 1;
                } else {
                  // 仅当满足以下条件时删除元素：
                  // - 不在表格内
                  // - 替换掉标记后文本为空
                  // - 原始文本仅包含标记
                  if (originalText === originalText.match(/😊([^😊]+)😊/g)?.join('')) {
                    elementsToRemove.push(i);
                    stats.removedRunElements = (stats.removedRunElements || 0) + 1;
                  } else {
                    // 如果原始文本不仅包含标记，则保留元素并修改文本
                    elementsToModify.push({ index: i, newText: cleanedText });
                    stats.removedTextMarkers = (stats.removedTextMarkers || 0) + 1;
                  }
                }
                continue;
              }
            }

            // 递归处理该子元素
            processElement(childElement, currentIsInTable);
          }

          // 先处理需要修改的元素
          for (const { index, newText } of elementsToModify) {
            const runElement = element.elements[index];
            // 找到w:t元素并修改其文本内容
            if (runElement && runElement.elements) {
              for (const childEl of runElement.elements) {
                if (childEl.type === 'element' && childEl.name === 'w:t' && childEl.elements) {
                  for (const textNode of childEl.elements) {
                    if (textNode.type === 'text') {
                      textNode.text = newText;
                    }
                  }
                }
              }
            }
          }

          // 然后从后往前删除标记的元素（从后往前删除避免索引变化）
          for (let i = elementsToRemove.length - 1; i >= 0; i--) {
            element.elements.splice(elementsToRemove[i], 1);
          }
        }
      };

      // 从body开始处理整个文档
      processElement(bodyElement);

      return stats;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 深拷贝 Word XML 元素，排除 parent 属性
   * @private
   * @param obj - 要拷贝的对象
   * @returns 深拷贝后的对象
   */
  private deepCloneWordElement(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    const clone: any = Array.isArray(obj) ? [] : {};

    for (const key in obj) {
      // 跳过 parent 属性和其他不需要的属性
      if (key === 'parent' || key === '_parent') {
        continue;
      }
      clone[key] = this.deepCloneWordElement(obj[key]);
    }

    return clone;
  }

  /**
   * 在指定段落后直接插入内容
   * @param paragraphId - 段落ID (带或不带标记)
   * @param content - 要插入的内容
   * @param contentStyle - 内容样式设置
   * @returns {string | null} 成功返回null，失败返回错误信息
   */
  public async insertContentAfterParagraph(
    paragraphId: string,
    content: string,
    contentStyle: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      color?: string;
      fontSize?: number;
      prefix?: string;
    } = {}
  ): Promise<string | null> {
    if (!this.docxObj) {
      throw new Error('文档未加载，请先调用 loadDocument()');
    }

    if (!paragraphId || !content) {
      return '段落ID和内容不能为空';
    }

    try {
      // 1. 查找目标段落
      const elementInfo = this.findElementById(paragraphId);
      if (!elementInfo || elementInfo.type !== 'paragraph') {
        return `未找到ID为 ${paragraphId} 的段落`;
      }

      // 2. 检查目标段落是否在表格内
      let isInTable = false;
      let tableElement: Element | null = null;
      // 检查路径是否存在且有效
      if (elementInfo.path && elementInfo.path.length > 0) {
        // 从段落的父元素开始向上遍历路径
        // elementInfo.path[elementInfo.path.length - 1] 是段落本身
        for (let i = elementInfo.path.length - 2; i >= 0; i--) {
          const ancestor = elementInfo.path[i].element;
          // 检查祖先元素是否是表格
          if (ancestor.type === 'element' && ancestor.name === 'w:tbl') {
            isInTable = true;
            tableElement = ancestor;
            // 找到最近的表格祖先，停止查找
            break;
          }
        }
      }

      // 3. 确定要在其后插入内容的元素（段落本身或其所在的表格）
      const targetElement = isInTable ? tableElement : elementInfo.element;

      // 如果找不到参考元素，则返回错误
      if (!targetElement) {
        return `无法找到用于确定插入位置的参考元素 (${isInTable ? '表格' : '段落'})`;
      }

      // 内部辅助函数：递归查找目标元素的父元素和索引
      const findParentAndIndex = (rootElement: Element | undefined, target: Element): { parent: Element | null; index: number } => {
        // 如果根元素无效或没有子元素，则查找失败
        if (!rootElement || !rootElement.elements) return { parent: null, index: -1 };

        // 遍历根元素的子元素
        for (let i = 0; i < rootElement.elements.length; i++) {
          const childElement = rootElement.elements[i];
          // 如果子元素是目标元素，则返回父元素和索引
          if (childElement === target) {
            return { parent: rootElement, index: i };
          }
          // 如果子元素是包含子元素的元素，则递归查找
          if (childElement && childElement.type === 'element' && childElement.elements) {
            const result = findParentAndIndex(childElement, target);
            // 如果在子树中找到，则返回结果
            if (result.parent) return result;
          }
        }
        // 在当前分支未找到目标元素
        return { parent: null, index: -1 };
      };

      // 4. 查找文档的 body 元素
      const docElement = this.docxObj.document.elements?.find(
        (el) => el.type === 'element' && el.name === 'w:document'
      );
      const bodyElement = docElement?.elements?.find(
        (el) => el.type === 'element' && el.name === 'w:body'
      );

      // 如果找不到 body 元素，则抛出错误
      if (!bodyElement) {
        throw new Error('无法找到文档内容主体 (w:body)');
      }

      // 5. 在 body 内查找目标元素（段落或表格）的父元素和索引
      const { parent: insertionParent, index: insertionIndex } = findParentAndIndex(bodyElement, targetElement);

      // 如果找不到插入位置，则返回错误
      if (!insertionParent || insertionIndex === -1) {
        return `无法确定目标 ${isInTable ? '表格' : '段落'} 在文档结构中的位置以进行插入`;
      }

      // 6. 处理内容中的表格和LaTeX公式
      const textColor = contentStyle.color || '090ac5';
      const { contentParts: finalContentParts } = await this.processContent(content, textColor);

      // 检查是否有表格内容
      const tableParts = finalContentParts.filter((part) => part.type === 'table' && part.tableStructure);

      // 7. 创建新段落的 XML 结构数组（多个段落）
      const paragraphs: Element[] = [];

      // 将非表格内容按照换行符分割成多个段落
      const processedTextParts: { type: string; parts: ContentPart[] }[] = [];

      // 先将连续的文本和公式内容收集起来作为段落
      let currentParagraphParts: ContentPart[] = [];

      // 遍历所有非表格内容
      for (const part of finalContentParts.filter((p) => p.type !== 'table')) {
        if (part.type === 'text') {
          // 对于文本内容，按换行符分割
          const textLines = part.content.split('\n');

          // 处理第一行（添加到当前段落）
          if (textLines.length > 0 && textLines[0].length > 0) {
            currentParagraphParts.push({
              type: 'text',
              content: textLines[0],
            });
          }

          // 处理剩余行（每行创建新段落）
          if (textLines.length > 1) {
            // 结束当前段落
            if (currentParagraphParts.length > 0) {
              processedTextParts.push({ type: 'paragraph', parts: [...currentParagraphParts] });
              currentParagraphParts = [];
            }

            // 处理中间行（每行一个独立段落）
            for (let i = 1; i < textLines.length - 1; i++) {
              if (textLines[i].length > 0) {
                processedTextParts.push({
                  type: 'paragraph',
                  parts: [{ type: 'text', content: textLines[i] }],
                });
              } else {
                // 空行也创建段落
                processedTextParts.push({ type: 'paragraph', parts: [] });
              }
            }

            // 处理最后一行（开始新段落）
            if (textLines[textLines.length - 1].length > 0) {
              currentParagraphParts.push({
                type: 'text',
                content: textLines[textLines.length - 1],
              });
            }
          }
        } else {
          // 对于公式内容，直接添加到当前段落
          currentParagraphParts.push(part);
        }
      }

      // 添加最后一个段落
      if (currentParagraphParts.length > 0) {
        processedTextParts.push({ type: 'paragraph', parts: currentParagraphParts });
      }

      // 为每个段落创建XML结构
      for (let i = 0; i < processedTextParts.length; i++) {
        const paragraphParts = processedTextParts[i].parts;

        // 创建完整的 w:p 段落结构
        const newParagraph: any = {
          type: 'element',
          name: 'w:p',
          elements: [],
        };

        // 只在第一个段落添加前缀（如果内容中没有包含"AI编辑"字符）
        if (i === 0 && contentStyle.prefix) {
          // 检查所有段落内容中是否已包含"AI编辑"字符
          const allContent = processedTextParts.map((p) => p.parts.map((part) => part.content).join('')).join('');
          const hasAIEditPrefix = allContent.includes('AI编辑');

          // 只有当内容中不包含"AI编辑"字符时才添加前缀
          if (!hasAIEditPrefix) {
            newParagraph.elements.push({
              type: 'element',
              name: 'w:r',
              elements: [
                {
                  type: 'element',
                  name: 'w:rPr',
                  elements: [
                    {
                      type: 'element',
                      name: 'w:b',
                      attributes: { 'w:val': 'true' },
                    },
                    {
                      type: 'element',
                      name: 'w:color',
                      attributes: { 'w:val': textColor },
                    },
                    {
                      type: 'element',
                      name: 'w:rFonts',
                      attributes: {
                        'w:ascii': 'Times New Roman',
                        'w:eastAsia': '宋体',
                        'w:hAnsi': 'Times New Roman',
                        'w:cs': 'Times New Roman',
                      },
                    },
                    {
                      type: 'element',
                      name: 'w:sz',
                      attributes: { 'w:val': '21' },
                    },
                    {
                      type: 'element',
                      name: 'w:szCs',
                      attributes: { 'w:val': '21' },
                    }
                  ],
                },
                {
                  type: 'element',
                  name: 'w:t',
                  elements: [
                    {
                      type: 'text',
                      text: contentStyle.prefix,
                    }
                  ],
                }
              ],
            });
          }
        }

        // 添加段落内容
        for (const part of paragraphParts) {
          if (part.type === 'text') {
            // 为文本创建运行元素
            const textRun = {
              type: 'element' as const,
              name: 'w:r',
              elements: [
                {
                  type: 'element' as const,
                  name: 'w:rPr',
                  elements: [] as Element[],
                },
                {
                  type: 'element' as const,
                  name: 'w:t',
                  elements: [
                    {
                      type: 'text' as const,
                      text: part.content,
                    }
                  ],
                }
              ],
            };

            // 应用样式到内容的 w:rPr
            const rPr: Element[] = textRun.elements.find((el) => el.name === 'w:rPr')?.elements ?? [];

            // 添加字体设置 - 中文宋体，西文Times New Roman
            rPr.push({
              type: 'element' as const,
              name: 'w:rFonts',
              attributes: {
                'w:ascii': 'Times New Roman',
                'w:eastAsia': '宋体',
                'w:hAnsi': 'Times New Roman',
                'w:cs': 'Times New Roman',
              },
            });

            // 添加字号设置 - 5号（10.5磅，对应半磅值为21）
            rPr.push({
              type: 'element' as const,
              name: 'w:sz',
              attributes: { 'w:val': '21' },
            });
            rPr.push({
              type: 'element' as const,
              name: 'w:szCs',
              attributes: { 'w:val': '21' },
            });

            // 添加粗体样式
            if (contentStyle.bold) {
              rPr.push({
                type: 'element' as const,
                name: 'w:b',
                attributes: { 'w:val': 'true' },
              });
            }
            // 添加斜体样式
            if (contentStyle.italic) {
              rPr.push({
                type: 'element' as const,
                name: 'w:i',
                attributes: { 'w:val': 'true' },
              });
            }
            // 添加下划线样式
            if (contentStyle.underline) {
              rPr.push({
                type: 'element' as const,
                name: 'w:u',
                attributes: { 'w:val': 'single' },
              });
            }
            // 添加颜色样式
            if (contentStyle.color) {
              rPr.push({
                type: 'element' as const,
                name: 'w:color',
                attributes: { 'w:val': contentStyle.color },
              });
            }
            // 如果提供了自定义字体大小，覆盖默认设置（Word中单位为半磅）
            if (contentStyle.fontSize) {
              // 找到并移除已添加的默认字号元素
              const szIndex = rPr.findIndex((el) => el.name === 'w:sz');
              if (szIndex !== -1) {
                rPr.splice(szIndex, 1);
              }
              const szCsIndex = rPr.findIndex((el) => el.name === 'w:szCs');
              if (szCsIndex !== -1) {
                rPr.splice(szCsIndex, 1);
              }

              const fontSizeHalfPoints = (contentStyle.fontSize * 2).toString();
              rPr.push({
                type: 'element' as const,
                name: 'w:sz',
                attributes: { 'w:val': fontSizeHalfPoints },
              });
              rPr.push({
                type: 'element' as const,
                name: 'w:szCs',
                attributes: { 'w:val': fontSizeHalfPoints },
              });
            }

            newParagraph.elements.push(textRun);
          } else if (part.type === 'formula' && part.formulaElement) {
            // 为公式创建运行元素
            const formulaRun = {
              type: 'element' as const,
              name: 'w:r',
              elements: [
                {
                  type: 'element' as const,
                  name: 'w:rPr',
                  elements: [],
                },
                part.formulaElement
              ],
            };
            newParagraph.elements.push(formulaRun);
          } else if (part.type === 'html' && part.htmlElement) {
            // 为HTML创建元素 - 直接添加转换后的OOXML元素
            if (part.htmlElement.elements) {
              // 如果HTML元素包含多个子元素，逐一添加
              for (const htmlChild of part.htmlElement.elements) {
                newParagraph.elements.push(htmlChild);
              }
            } else {
              // 如果HTML元素是单个元素，直接添加
              newParagraph.elements.push(part.htmlElement);
            }
          }
        }

        paragraphs.push(newParagraph);
      }

      // 8. 在目标元素之后插入所有创建的段落和表格
      // 确保 insertionParent.elements 存在
      if (!insertionParent.elements) {
        insertionParent.elements = [];
      }

      // 插入顺序：先插入段落，然后插入表格
      let insertPos = insertionIndex + 1;

      // 如果目标元素是表格，先插入一个空行段落
      if ((isInTable || (targetElement && targetElement.name === 'w:tbl')) && contentStyle.prefix?.includes('答案')) {
        const emptyParagraph: Element = {
          type: 'element',
          name: 'w:p',
          elements: [],
        };
        insertionParent.elements.splice(insertPos, 0, emptyParagraph);
        insertPos++;
      }

      // 插入段落
      for (const paragraph of paragraphs) {
        insertionParent.elements.splice(insertPos, 0, paragraph);
        insertPos++;
      }

      // 插入表格
      for (const part of tableParts) {
        if (part.type === 'table' && part.tableStructure) {
          insertionParent.elements.splice(insertPos, 0, part.tableStructure);
          insertPos++;
        }
      }

      // 9. 标记文档已被修改
      this.isModified = true;

      // 插入成功，返回 null
      return null;
    } catch (error) {
      this.logger.info('插入内容时出错:', error);
      // 返回错误信息
      return error instanceof Error ? error.message : '插入内容时发生未知错误';
    }
  }

  // 处理节点的错误信息，添加批注
  public async processErrorInfo(node: any) {
    if (node.error_info && Array.isArray(node.error_info) && node.error_info.length > 0) {
      // 先收集所有需要处理的错误信息和对应的元素
      const errorProcessingTasks: Array<{
        paraId: string;
        error: any;
        elementInfo: any;
      }> = [];

      // 第一步：查找所有元素并收集任务
      for (const error of node.error_info.flat()) {
        // 使用 symbol_idx 作为段落ID
        const paraId = error.symbol_idx || error.id ||
          extractSymbolId(node.content.body + (node.content.choices || [])?.map((v) => v?.option || '').join(''));

        // 确保 paraId 存在且不为空
        if (paraId) {
          // 查找元素（在结构还未被修改时进行查找）
          const elementInfo = this.findElementById(`${paraId}`);
          if (elementInfo) {
            errorProcessingTasks.push({
              paraId: `${paraId}`,
              error,
              elementInfo,
            });
          } else {
            this.logger.info(`未找到ID为 ${paraId} 的元素，跳过该批注`);
          }
        }
      }

      // 第二步：批量处理所有任务
      for (const task of errorProcessingTasks) {
        try {
          // 获取错误信息和修改建议
          const errorInfo = task.error.error_info || '';
          const fixInfo = task.error.fix_info || '';
          // 获取错误级别，如果没有则默认为0
          const level = typeof task.error.level === 'number' ? task.error.level : 0;

          // 直接使用缓存的元素信息，避免重复查找
          await this.addCommentToElementByElementInfo(
            task.elementInfo,
            errorInfo,
            fixInfo,
            level,
            {
              author: 'AI编辑',
              initials: 'SYS',
              targetText: task.error.keywords,
            }
          );
        } catch (e) {
          this.logger.info(`为段落 ${task.paraId} 添加批注失败:`, e);
        }
      }
    }
  }

  // 处理节点的AI信息，添加答案和解析
  public async processAgentInfo(node: any) {
    if (!node.agent_info) {
      return;
    }

    try {
      // 优先处理 agent_res_json 逻辑（多题情况）
      if (
        node.agent_info.agent_res_json &&
         Array.isArray(node.agent_info.agent_res_json) &&
        node.agent_info.agent_res_json.length > 1 &&
        node.quest_html &&
        0
      ) {

        // 去除标点符号的函数
        const removePunctuation = (text: string): string => {
          return text.replace(/[^\w\s\u4e00-\u9fff]/g, '').replace(/\s+/g, '');
        };

        // 为每个 quest_number 在 quest_html 中查找位置
        const questPositions: { questNumber: string, position: number, agentResItem: any }[] = [];

        for (const agentResItem of node.agent_info.agent_res_json) {
          const questNumber = agentResItem.quest_number;
          if (!questNumber) continue;

          const cleanQuestNumber = removePunctuation(questNumber);

          // 在 quest_html 中查找包含该题号的位置
          let found = false;
          let position = -1;

          // 1. 首先尝试在 qnum 标签中查找
          const qnumRegex = /<qnum(\d+)>(.*?)<\/qnum\1>/g;
          let qnumMatch;
          while ((qnumMatch = qnumRegex.exec(node.quest_html)) !== null) {
            const qnumContent = qnumMatch[2];
            const cleanQnumContent = removePunctuation(qnumContent);

            if (cleanQnumContent === cleanQuestNumber || qnumMatch[1] === questNumber) {
              found = true;
              position = qnumMatch.index;
              break;
            }
          }

          // 2. 如果在 qnum 中没找到，尝试在整个 quest_html 中直接查找
          if (!found) {
            const questNumberIndex = node.quest_html.indexOf(questNumber);
            if (questNumberIndex !== -1) {
              found = true;
              position = questNumberIndex;
            }
          }

          if (found) {
            questPositions.push({
              questNumber,
              position,
              agentResItem,
            });
          }
        }

        // 只有当所有 quest_number 都能在 quest_html 中找到时才继续处理
        if (questPositions.length === node.agent_info.agent_res_json.length) {
          // 按位置排序
          questPositions.sort((a, b) => a.position - b.position);

          // 根据位置分组 quest_html
          const questGroups: { questNumber: string, fullContent: string, agentResItem: any }[] = [];

          for (let i = 0; i < questPositions.length; i++) {
            const currentQuest = questPositions[i];
            const nextQuest = questPositions[i + 1];

            // 计算当前分组的完整内容范围
            const startPos = currentQuest.position;
            const endPos = nextQuest ? nextQuest.position : node.quest_html.length;

            const fullContent = node.quest_html.substring(startPos, endPos);

            questGroups.push({
              questNumber: currentQuest.questNumber,
              fullContent,
              agentResItem: currentQuest.agentResItem,
            });
          }

          // 为每个分组找到对应的 symbol 并插入内容
          for (const questGroup of questGroups) {
            const agentResItem = questGroup.agentResItem;

            // 在对应分组的完整内容中查找 symbol
            const fullContent = questGroup.fullContent;
            const symbolMatches = fullContent.match(/symbol="([^"]+)"/g) || [];
            const symbols: string[] = [];

            for (const symbolMatch of symbolMatches) {
              const symbolResult = symbolMatch.match(/symbol="([^"]+)"/);
              if (symbolResult && symbolResult[1]) {
                symbols.push(symbolResult[1]);
              }
            }

            if (symbols.length > 0) {
              // 找出最大的 symbol
              const maxSymbol = symbols.reduce((prev, current) => {
                const prevNum = parseInt(prev.replace(/\D/g, ''), 10);
                const currentNum = parseInt(current.replace(/\D/g, ''), 10);

                if (isNaN(prevNum) || isNaN(currentNum)) {
                  return prev > current ? prev : current;
                }

                return prevNum > currentNum ? prev : current;
              });

              // 插入解析
              if (agentResItem.analysis) {
                const res = await this.insertContentAfterParagraph(
                  `${maxSymbol}`,
                  agentResItem.analysis,
                  {
                    color: '090ac5',
                    prefix: '【AI编辑：解析】',
                    bold: false,
                  }
                );
                if (res) {
                  this.logger.info(`在段落 ${maxSymbol} 后插入解析失败`, res);
                }
              }

              // 插入答案
              if (agentResItem.answer) {
                this.logger.info(`在段落 ${maxSymbol} 后插入答案： ${agentResItem.answer}`);
                const res = await this.insertContentAfterParagraph(
                  `${maxSymbol}`,
                  agentResItem.answer,
                  {
                    color: '090ac5',
                    prefix: '【AI编辑：答案】',
                    bold: false,
                  }
                );
                if (res) {
                  this.logger.info(`在段落 ${maxSymbol} 后插入答案失败`, res);
                }
              }
            }
          }
        }
        return;
      }

      // 原有逻辑：处理单题情况
      if (node.agent_info.agent_answer || node.agent_info.agent_analysis) {
        // 存储所有找到的 symbol
        const symbols: string[] = [];
        // 如果在内容中没找到 symbol，也尝试从 quest_html 中查找
        if (symbols.length === 0 && node.quest_html) {
          const htmlMatches = node.quest_html.match(/symbol="([^"]+)"/g) || [];
          for (const match of htmlMatches) {
            const symbolMatch = match.match(/symbol="([^"]+)"/);
            if (symbolMatch && symbolMatch[1]) {
              symbols.push(symbolMatch[1]);
            }
          }
        }

        // 如果找到了 symbol
        if (symbols.length > 0) {
          // 找出最大的 symbol（假设是数字格式，如果是其他格式可能需要修改比较逻辑）
          const maxSymbol = symbols.reduce((prev, current) => {
            // 尝试将 symbol 转换为数字进行比较
            const prevNum = parseInt(prev.replace(/\D/g, ''), 10);
            const currentNum = parseInt(current.replace(/\D/g, ''), 10);

            // 如果转换失败或无法比较，则使用字符串比较
            if (isNaN(prevNum) || isNaN(currentNum)) {
              return prev > current ? prev : current;
            }

            return prevNum > currentNum ? prev : current;
          });

          // 处理 agent_info 内容
          if (node.agent_info.agent_analysis) {
            // console.info(`在段落 ${maxSymbol} 后插入解析： ${node.agent_info.agent_analysis}`);
            const res = await this.insertContentAfterParagraph(
              `${maxSymbol}`,
              node.agent_info.agent_analysis,
              {
                // 使用深蓝色
                color: '090ac5',
                prefix: '【AI编辑：解析】',
                bold: false,
              }
            );
            if (res) {
              // this.logger.info(`在段落 ${maxSymbol} 后插入解析失败`, res);
            }
          }
          if (node.agent_info.agent_answer) {
            this.logger.info(`在段落 ${maxSymbol} 后插入答案： ${node.agent_info.agent_answer}`);
            const res = await this.insertContentAfterParagraph(
              `${maxSymbol}`,
              node.agent_info.agent_answer,
              {
                // 使用深蓝色
                color: '090ac5',
                prefix: '【AI编辑：答案】',
                bold: false,
              }
            );
            if (res) {
              this.logger.info(`在段落 ${maxSymbol} 后插入答案失败`, res);
            }
          }
        }
      }
    } catch (e) {
      throw new Error('处理agent_info时出错:');
    }
  }

  /**
   * 初始化一个仅包含文本的contentParts
   * @param content 文本内容
   */
  private initContentParts(content: string): ContentPart[] {
    return [{
      type: 'text',
      content,
    }];
  }

  /**
   * 提取并处理内容中的表格
   * @param contentParts 内容部分数组
   * @param tableColor 表格文本和边框颜色，如 "090ac5"
   * @returns 包含表格的内容部分数组
   */
  private async processTables(contentParts: ContentPart[], tableColor = ''): Promise<ContentPart[]> {
    if (!contentParts || contentParts.length === 0) {
      return [];
    }

    const result: ContentPart[] = [];

    // HTML 表格正则表达式
    const tablePattern = /<table[\s\S]*?<\/table>/gi;

    // 处理每个内容部分
    for (const part of contentParts) {
      // 只处理文本类型的内容
      if (part.type !== 'text') {
        result.push(part);
        continue;
      }

      const content = part.content;

      // 检查文本中是否包含HTML表格
      if (!tablePattern.test(content)) {
        result.push(part);
        continue;
      }

      // 重置正则表达式
      tablePattern.lastIndex = 0;

      // 找出所有表格和它们的位置
      const tables: { html: string, start: number, end: number }[] = [];
      let match;

      while ((match = tablePattern.exec(content)) !== null) {
        tables.push({
          html: match[0],
          start: match.index,
          end: match.index + match[0].length,
        });
      }

      // 如果没有找到表格，保留原文本
      if (tables.length === 0) {
        result.push(part);
        continue;
      }

      // 处理找到的表格
      let lastPos = 0;
      for (const table of tables) {
        // 添加表格前的文本
        if (table.start > lastPos) {
          const beforeText = content.substring(lastPos, table.start);
          if (beforeText.trim()) {
            result.push({
              type: 'text',
              content: beforeText,
            });
          }
        }

        try {
          // 调用 API 将 HTML 表格转换为 OOXML，直接传递颜色参数
          const tableOoxml = await this.convertHtmlTableToOoxml(table.html, tableColor);

          // 添加表格
          if (tableOoxml) {
            result.push({
              type: 'table',
              content: table.html,
              tableStructure: tableOoxml,
            });
          } else {
            // 如果转换失败，保留原始表格文本
            result.push({
              type: 'text',
              content: table.html,
            });
          }
        } catch (error) {
          this.logger.info('处理表格时出错:', error);
          // 如果处理失败，保留原始表格文本
          result.push({
            type: 'text',
            content: table.html,
          });
        }

        lastPos = table.end;
      }

      // 添加最后一个表格后的文本
      if (lastPos < content.length) {
        const afterText = content.substring(lastPos);
        if (afterText.trim()) {
          result.push({
            type: 'text',
            content: afterText,
          });
        }
      }
    }

    return result;
  }

  /**
   * 调用 API 将 HTML 表格转换为 OOXML
   * @param htmlTable HTML 格式的表格
   * @param tableColor 表格文本和边框颜色，默认为空
   * @returns OOXML 表格结构
   */
  private async convertHtmlTableToOoxml(htmlTable: string, tableColor = ''): Promise<any> {
    try {
      // 调用 API 将 HTML 转换为 OOXML
      const url = 'http://content-server.hexinedu.com/api/content/open/html2ooxml';

      const response = await axios.post(url,
        { html: htmlTable },
        { headers: { 'x-request-from': 'hexin' } }
      );

      const apiResult = response.data;
      if (apiResult.status === 0 && apiResult.data) {
        // 将 XML 字符串解析为对象
        try {
          const ooxmlString = apiResult.data.replace(/<m:oMath>(.*?)<\/m:oMath>/g, (match) => {
            return match.replace(/<m:rad>(.*?)<\/m:rad>/g, (...args) => {
              return `<m:rad>
                        <m:radPr>
                        <m:ctrlPr>
                        <w:rPr>
                        <w:color w:val="${tableColor.split('#').join('')}"/>
                        </w:rPr>
                        </m:ctrlPr>
                        </m:radPr>${args[1]}
                      </m:rad>`;
            })
              .replace(/<m:r>/g, (...args) => {
                if (!tableColor) {
                  return args[0];
                }
                return '<m:r>' +
                  '<w:rPr>' +
                  `<w:color w:val="${(tableColor!.split('#').join(''))}"/>` +
                  '</w:rPr>';
              })
              .replace(/<m:sub>(.*?)<\/m:sub>/g, (...args) => {
                const msub = args[1];
                // 若 sub 标签的内容是中文才进行处理
                return `<m:sub>${msub.replace(/<m:t>([\u4e00-\u9fa5]+)<\/m:t>/g,
                  `<w:rPr><w:color w:val="${tableColor.split('#').join('')}"/></w:rPr><m:t>$1<\/m:t>`)}<\/m:sub>`;
              })
              .replace(/<\/m:dPr>/g, `
                    <m:ctrlPr>
                        <w:rPr>
                            <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                            <w:color w:val="${tableColor.split('#').join('')}"/>
                        </w:rPr>
                    </m:ctrlPr>
                </m:dPr>`)
              .replace(/<\/m:fPr>/g, `
                    <m:ctrlPr>
                        <w:rPr>
                            <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                            <w:color w:val="${tableColor.split('#').join('')}"/>
                        </w:rPr>
                    </m:ctrlPr>
                </m:fPr>`)
              .replace(/<\/w:rPr>/g, `<w:color w:val="${tableColor.split('#').join('')}"/><w:sz w:val="21" /></w:rPr>`)
              .replace(/<w:sz[^>]*>/g, '<w:sz w:val="21" />')
              .replace(/000000/g, tableColor.split('#').join(''));
          });

          const ooxmlObj = xml2js(ooxmlString, this.parseOptions);

          if (ooxmlObj && ooxmlObj.elements && ooxmlObj.elements.length > 0) {
            const tableElement = ooxmlObj.elements[0];

            // 如果有颜色参数，为表格添加边框和颜色样式
            if (tableColor) {
              this.addOoxmlTableStyles(tableElement, tableColor);
            }

            return tableElement;
          }
        } catch (parseError) {
          this.logger.info('解析表格 OOXML 时出错:', parseError);
        }
      } else {
        this.logger.info('表格转换 API 返回失败:', JSON.stringify(apiResult));
      }

      return null;
    } catch (error) {
      this.logger.info('调用表格转换 API 出错:', error);
      return null;
    }
  }

  /**
   * 为 OOXML 表格元素添加边框和颜色样式
   * @param tableElement OOXML 表格元素
   * @param color 颜色，如 "090ac5"
   */
  private addOoxmlTableStyles(tableElement: Element, color: string): void {
    if (!tableElement || !tableElement.elements) return;
    const colorHex = color.startsWith('#') ? color.substring(1) : color;
    // 递归查找并处理所有表格相关元素
    const processElement = (element: Element) => {
      if (!element || !element.elements) return;
      // 处理表格属性 - 添加边框样式
      if (element.name === 'w:tbl') {
        // 查找表格属性元素
        let tblPrElement: any = element.elements.find((el) => el.name === 'w:tblPr');
        // 如果不存在则创建
        if (!tblPrElement) {
          tblPrElement = {
            type: 'element',
            name: 'w:tblPr',
            elements: [],
          };
          // 添加到表格元素的开头
          element.elements.unshift(tblPrElement);
        } else if (!tblPrElement.elements) {
          tblPrElement.elements = [];
        }
        // 查找表格边框元素
        let tblBordersElement: any = tblPrElement.elements.find((el) => el.name === 'w:tblBorders');
        // 如果不存在则创建
        if (!tblBordersElement) {
          tblBordersElement = {
            type: 'element',
            name: 'w:tblBorders',
            elements: [],
          };
          tblPrElement.elements.push(tblBordersElement);
        } else if (!tblBordersElement.elements) {
          tblBordersElement.elements = [];
        }
        // 定义边框样式 (上、左、下、右、内水平线、内垂直线)
        const borderTypes = ['top', 'left', 'bottom', 'right', 'insideH', 'insideV'];
        // 对每种边框添加样式
        for (const borderType of borderTypes) {
          // 检查是否已存在该边框
          const existingBorder = tblBordersElement.elements.find((el) =>
            el.name === `w:${borderType}`
          );
          if (existingBorder) {
            // 如果存在，更新其样式
            if (!existingBorder.elements) existingBorder.elements = [];
            existingBorder.attributes = {
              'w:val': 'single',
              'w:sz': '4', // 边框宽度
              'w:space': '0',
              'w:color': colorHex,
            };
          } else {
            // 如果不存在，创建新边框
            tblBordersElement.elements.push({
              type: 'element',
              name: `w:${borderType}`,
              attributes: {
                'w:val': 'single',
                'w:sz': '4',
                'w:space': '0',
                'w:color': colorHex,
              },
            });
          }
        }
      }

      // 处理单元格属性 - 添加边框和颜色
      if (element.name === 'w:tc') {
        // 查找单元格属性元素
        let tcPrElement: any = element.elements.find((el) => el.name === 'w:tcPr');

        // 如果不存在则创建
        if (!tcPrElement) {
          tcPrElement = {
            type: 'element',
            name: 'w:tcPr',
            elements: [],
          };
          // 添加到单元格元素的开头
          element.elements.unshift(tcPrElement);
        } else if (!tcPrElement.elements) {
          tcPrElement.elements = [];
        }

        // 查找单元格边框元素
        let tcBordersElement = tcPrElement.elements.find((el) => el.name === 'w:tcBorders');

        // 如果不存在则创建
        if (!tcBordersElement) {
          tcBordersElement = {
            type: 'element',
            name: 'w:tcBorders',
            elements: [],
          };
          tcPrElement.elements.push(tcBordersElement);
        } else if (!tcBordersElement.elements) {
          tcBordersElement.elements = [];
        }

        // 定义单元格边框样式 (上、左、下、右)
        const cellBorderTypes = ['top', 'left', 'bottom', 'right'];

        // 对每种边框添加样式
        for (const borderType of cellBorderTypes) {
          // 检查是否已存在该边框
          const existingBorder = tcBordersElement.elements.find((el) =>
            el.name === `w:${borderType}`
          );

          if (existingBorder) {
            // 如果存在，更新其样式
            existingBorder.attributes = {
              'w:val': 'single',
              'w:sz': '4',
              'w:space': '0',
              'w:color': colorHex,
            };
          } else {
            // 如果不存在，创建新边框
            tcBordersElement.elements.push({
              type: 'element',
              name: `w:${borderType}`,
              attributes: {
                'w:val': 'single',
                'w:sz': '4',
                'w:space': '0',
                'w:color': colorHex,
              },
            });
          }
        }
      }

      // 处理段落文本颜色
      if (element.name === 'w:p') {
        for (const childElement of element.elements || []) {
          if (childElement.name === 'w:r' && childElement.elements) {
            // 查找运行属性元素
            let rPrElement: any = childElement.elements.find((el) => el.name === 'w:rPr');

            // 如果不存在则创建
            if (!rPrElement) {
              rPrElement = {
                type: 'element',
                name: 'w:rPr',
                elements: [],
              };
              // 添加到运行元素的开头
              childElement.elements.unshift(rPrElement);
            } else if (!rPrElement.elements) {
              rPrElement.elements = [];
            }

            // 查找颜色元素
            const colorElement = rPrElement.elements.find((el) => el.name === 'w:color');

            // 如果不存在则创建，否则更新
            if (!colorElement) {
              rPrElement.elements.push({
                type: 'element',
                name: 'w:color',
                attributes: { 'w:val': colorHex },
              });
            } else {
              colorElement.attributes = { 'w:val': colorHex };
            }
          }
        }
      }

      // 递归处理子元素
      for (const childElement of element.elements) {
        processElement(childElement);
      }
    };

    // 从表格元素开始处理
    processElement(tableElement);
  }

  /**
   * 处理内容中的LaTeX公式
   * @param contentParts 内容部分数组
   * @param textColor 公式文本颜色
   * @returns 包含公式的内容部分数组
   */
  private async processLatex(contentParts: ContentPart[], textColor = ''): Promise<{
    contentParts: ContentPart[],
    hasFormula: boolean
  }> {
    if (!contentParts || contentParts.length === 0) {
      return { contentParts: [], hasFormula: false };
    }

    const result: ContentPart[] = [];
    let hasFormula = false;

    // LaTeX公式正则
    const latexPattern = /\$\$(.*?)\$\$/g;

    // 处理每个内容部分
    for (const part of contentParts) {
      // 只处理文本类型的内容
      if (part.type !== 'text') {
        result.push(part);
        continue;
      }

      const content = part.content;

      // 收集所有LaTeX公式及其在原文中的信息
      const formulaInfo: { original: string, latex: string, position: number }[] = [];
      const latexStrings: string[] = [];

      // 重置正则表达式
      latexPattern.lastIndex = 0;
      let match;

      // 找出所有公式及其位置
      while ((match = latexPattern.exec(content)) !== null) {
        const fullMatch = match[0];
        const latexOnly = match[1];
        const position = match.index;

        if (latexOnly.trim()) {
          formulaInfo.push({ original: fullMatch, latex: latexOnly, position });
          latexStrings.push(latexOnly);
        }
      }

      // 如果没有找到公式，保持原文本
      if (formulaInfo.length === 0) {
        result.push(part);
        continue;
      }

      // --- Batch Processing ---
      let batchResults: any[] = [];
      let useBatchAPI = true;

      try {
        // 调用API批量将LaTeX转换为OOXML
        const url = 'http://content-server.hexinedu.com/api/content/open/batch/latex2ooxml';

        const response = await axios.post(url,
          { latexs: latexStrings, options: { outputType: 'word' } },
          { headers: { 'x-request-from': 'hexin' } }
        );

        const apiResult = response.data;

        if (apiResult.status === 0 && apiResult.data && apiResult.data.length > 0) {
          const resultsMap = new Map();
          batchResults = apiResult.data.map((res, index) => {
            resultsMap.set(index, res);
            return {
              latex: latexStrings[index],
              ooxml: res
                .replace(/<m:rad>(.*?)<\/m:rad>/g, (...args) => {
                  return `<m:rad>
                            <m:radPr>
                            <m:ctrlPr>
                            <w:rPr>
                            <w:color w:val="${textColor.split('#').join('')}"/>
                            </w:rPr>
                            </m:ctrlPr>
                            </m:radPr>${args[1]}
                          </m:rad>`;
                })
                .replace(/<m:r>/g, (...args) => {
                  if (!textColor) {
                    return args[0];
                  }
                  return '<m:r>' +
                    '<w:rPr>' +
                    `<w:color w:val="${(textColor!.split('#').join(''))}"/>` +
                    '</w:rPr>';
                })
                .replace(/<m:sub>(.*?)<\/m:sub>/g, (...args) => {
                  const msub = args[1];
                  // 若 sub 标签的内容是中文才进行处理
                  return `<m:sub>${msub.replace(/<m:t>([\u4e00-\u9fa5]+)<\/m:t>/g,
                    `<w:rPr><w:color w:val="${textColor.split('#').join('')}"/></w:rPr><m:t>$1<\/m:t>`)}<\/m:sub>`;
                })
                .replace(/<\/m:dPr>/g, `
                        <m:ctrlPr>
                            <w:rPr>
                                <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                                <w:color w:val="${textColor.split('#').join('')}"/>
                            </w:rPr>
                        </m:ctrlPr>
                    </m:dPr>`)
                .replace(/<\/m:fPr>/g, `
                        <m:ctrlPr>
                            <w:rPr>
                                <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                                <w:color w:val="${textColor.split('#').join('')}"/>
                            </w:rPr>
                        </m:ctrlPr>
                    </m:fPr>`)
                .replace(/<\/w:rPr>/g, `<w:color w:val="${textColor.split('#').join('')}"/><w:sz w:val="21" /></w:rPr>`)
                .replace(/<w:sz[^>]*>/g, '<w:sz w:val="21" />')
                .replace(/000000/g, textColor.split('#').join('')),
            };
          });
          hasFormula = true;
        } else {
          this.logger.info('批量处理LaTeX公式API返回失败或无数据，切换到单个处理模式:', JSON.stringify(apiResult));
          useBatchAPI = false;
        }
      } catch (error) {
        this.logger.info('调用批量处理LaTeX公式API出错，切换到单个处理模式:', JSON.stringify(error));
        useBatchAPI = false;
      }

      // --- Single Processing Fallback ---
      if (!useBatchAPI) {
        this.logger.info('开始使用单个接口逐一处理LaTeX公式');
        batchResults = [];

        for (let i = 0; i < latexStrings.length; i++) {
          const latex = latexStrings[i];

          try {
            const singleUrl = 'http://content-server.hexinedu.com/api/content/open/latex2ooxml';

            const singleResponse = await axios.post(singleUrl,
              { latex: latex },
              { headers: { 'x-request-from': 'hexin' } }
            );

            const singleResult = singleResponse.data;

            if (singleResult.status === 0 && singleResult.data) {
              batchResults.push({
                latex: latex,
                ooxml: singleResult.data
                  .replace(/<m:rad>(.*?)<\/m:rad>/g, (...args) => {
                    return `<m:rad>
                              <m:radPr>
                              <m:ctrlPr>
                              <w:rPr>
                              <w:color w:val="${textColor.split('#').join('')}"/>
                              </w:rPr>
                              </m:ctrlPr>
                              </m:radPr>${args[1]}
                            </m:rad>`;
                  })
                  .replace(/<m:r>/g, (...args) => {
                    if (!textColor) {
                      return args[0];
                    }
                    return '<m:r>' +
                      '<w:rPr>' +
                      `<w:color w:val="${(textColor!.split('#').join(''))}"/>` +
                      '</w:rPr>';
                  })
                  .replace(/<m:sub>(.*?)<\/m:sub>/g, (...args) => {
                    const msub = args[1];
                    // 若 sub 标签的内容是中文才进行处理
                    return `<m:sub>${msub.replace(/<m:t>([\u4e00-\u9fa5]+)<\/m:t>/g,
                      `<w:rPr><w:color w:val="${textColor.split('#').join('')}"/></w:rPr><m:t>$1<\/m:t>`)}<\/m:sub>`;
                  })
                  .replace(/<\/m:dPr>/g, `
                          <m:ctrlPr>
                              <w:rPr>
                                  <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                                  <w:color w:val="${textColor.split('#').join('')}"/>
                              </w:rPr>
                          </m:ctrlPr>
                      </m:dPr>`)
                  .replace(/<\/m:fPr>/g, `
                          <m:ctrlPr>
                              <w:rPr>
                                  <w:rFonts w:ascii="Cambria Math" w:hAnsi="Cambria Math"/>
                                  <w:color w:val="${textColor.split('#').join('')}"/>
                              </w:rPr>
                          </m:ctrlPr>
                      </m:fPr>`)
                  .replace(/<\/w:rPr>/g, `<w:color w:val="${textColor.split('#').join('')}"/><w:sz w:val="21" /></w:rPr>`)
                  .replace(/<w:sz[^>]*>/g, '<w:sz w:val="21" />')
                  .replace(/000000/g, textColor.split('#').join('')),
              });
              hasFormula = true;
            } else {
              this.logger.info(`单个LaTeX公式处理失败: ${latex}，API返回:`, JSON.stringify(singleResult));
              // 对于失败的公式，添加一个空结果，保持索引对应
              batchResults.push({
                latex: latex,
                ooxml: null,
              });
            }
          } catch (singleError) {
            this.logger.info(`处理单个LaTeX公式时出错: ${latex}`, JSON.stringify(singleError));
            // 对于出错的公式，添加一个空结果，保持索引对应
            batchResults.push({
              latex: latex,
              ooxml: null,
            });
          }
        }
      }
      // --- End Processing ---

      // 创建一个包含文本和公式的有序列表
      const partContentParts: ContentPart[] = [];
      let lastPosition = 0;

      for (const formula of formulaInfo) {
        if (formula.position > lastPosition) {
          partContentParts.push({
            type: 'text',
            content: content.substring(lastPosition, formula.position),
          });
        }

        const formulaResult = batchResults.find((res) => res.latex === formula.latex);
        if (formulaResult && formulaResult.ooxml) {
          try {
            const ooxmlStr = formulaResult.ooxml.replace(/a:/g, 'm:');
            const ooxmlObj = xml2js(ooxmlStr, this.parseOptions);
            if (ooxmlObj.elements && ooxmlObj.elements.length > 0) {
              const formulaElement = ooxmlObj.elements[0];
              partContentParts.push({
                type: 'formula',
                content: formula.original,
                formulaElement,
              });
            } else {
              this.logger.info(`无法解析公式 "${formula.original}" 的OOXML.`);
              partContentParts.push({
                type: 'text',
                content: formula.original,
              });
            }
          } catch (parseError) {
            this.logger.info(`解析公式 "${formula.original}" 的OOXML时出错:`, parseError);
            partContentParts.push({
              type: 'text',
              content: formula.original,
            });
          }
        } else {
          partContentParts.push({
            type: 'text',
            content: formula.original,
          });
        }
        lastPosition = formula.position + formula.original.length;
      }

      if (lastPosition < content.length) {
        partContentParts.push({
          type: 'text',
          content: content.substring(lastPosition),
        });
      }

      // 将处理后的内容部分添加到结果中
      result.push(...partContentParts);
    }

    return { contentParts: result, hasFormula };
  }

  /**
   * 处理内容中的HTML标签
   * @param contentParts 内容部分数组
   * @param textColor HTML文本颜色
   * @returns 包含HTML的内容部分数组
   */
  private async processHtml(contentParts: ContentPart[], textColor = ''): Promise<ContentPart[]> {
    if (!contentParts || contentParts.length === 0) {
      return [];
    }

    const result: ContentPart[] = [];

    // HTML标签正则表达式 - 匹配简单的HTML标签及其内容
    const htmlPattern = /<[^>]+>.*?<\/[^>]+>/g;

    // 处理每个内容部分
    for (const part of contentParts) {
      // 只处理文本类型的内容
      if (part.type !== 'text') {
        result.push(part);
        continue;
      }

      const content = part.content;

      // 检查文本中是否包含HTML标签
      if (!htmlPattern.test(content)) {
        result.push(part);
        continue;
      }

      // 重置正则表达式
      htmlPattern.lastIndex = 0;

      // 找出所有HTML标签和它们的位置
      const htmlTags: { html: string, start: number, end: number }[] = [];
      let match;

      while ((match = htmlPattern.exec(content)) !== null) {
        htmlTags.push({
          html: match[0],
          start: match.index,
          end: match.index + match[0].length,
        });
      }

      // 如果没有找到HTML标签，保留原文本
      if (htmlTags.length === 0) {
        result.push(part);
        continue;
      }

      // 处理找到的HTML标签
      let lastPos = 0;
      for (const htmlTag of htmlTags) {
        // 添加HTML标签前的文本
        if (htmlTag.start > lastPos) {
          const beforeText = content.substring(lastPos, htmlTag.start);
          if (beforeText.trim()) {
            result.push({
              type: 'text',
              content: beforeText,
            });
          }
        }

        try {
          // 调用 API 将 HTML 转换为 OOXML
          const htmlOoxml = await this.convertHtmlToOoxml(htmlTag.html, textColor);

          // 添加HTML内容
          if (htmlOoxml) {
            result.push({
              type: 'html',
              content: htmlTag.html,
              htmlElement: htmlOoxml,
            });
          } else {
            // 如果转换失败，保留原始HTML文本
            result.push({
              type: 'text',
              content: htmlTag.html,
            });
          }
        } catch (error) {
          this.logger.info('处理HTML标签时出错:', error);
          // 如果处理失败，保留原始HTML文本
          result.push({
            type: 'text',
            content: htmlTag.html,
          });
        }

        lastPos = htmlTag.end;
      }

      // 添加最后一个HTML标签后的文本
      if (lastPos < content.length) {
        const afterText = content.substring(lastPos);
        if (afterText.trim()) {
          result.push({
            type: 'text',
            content: afterText,
          });
        }
      }
    }

    return result;
  }

  /**
   * 调用 API 将 HTML 转换为 OOXML
   * @param html HTML 内容
   * @param textColor 文本颜色，默认为空
   * @returns OOXML 结构
   */
  private async convertHtmlToOoxml(html: string, textColor = ''): Promise<any> {
    try {
      // 调用 API 将 HTML 转换为 OOXML
      const url = 'http://content-server.hexinedu.com/api/content/open/html2ooxml';

      const response = await axios.post(url,
        { html: html },
        { headers: { 'x-request-from': 'hexin' } }
      );

      const apiResult = response.data;
      if (apiResult.status === 0 && apiResult.data) {
        // 将 XML 字符串解析为对象
        try {
          let ooxmlString = apiResult.data;

          // 如果有颜色参数，为HTML内容添加颜色样式
          if (textColor) {
            const colorHex = textColor.startsWith('#') ? textColor.substring(1) : textColor;
            // 为HTML内容添加颜色
            ooxmlString = ooxmlString.replace(/<w:r>/g, `<w:r><w:rPr><w:color w:val="${colorHex}"/></w:rPr>`);
            // 如果已经有rPr，则在其中添加颜色
            ooxmlString = ooxmlString.replace(/<w:rPr>/g, `<w:rPr><w:color w:val="${colorHex}"/>`);
          }

          const ooxmlObj = xml2js(ooxmlString, this.parseOptions);

          if (ooxmlObj && ooxmlObj.elements && ooxmlObj.elements.length > 0) {
            return ooxmlObj.elements[0];
          }
        } catch (parseError) {
          this.logger.info('解析HTML OOXML时出错:', parseError);
        }
      } else {
        this.logger.info('HTML转换 API 返回失败:', JSON.stringify(apiResult));
      }

      return null;
    } catch (error) {
      this.logger.info('调用HTML转换 API 出错:', error);
      return null;
    }
  }

  /**
   * 内部方法：根据表格颜色处理 contentParts 中的内容
   * @param errorInfo 错误信息文本
   * @param textColor 文本颜色
   * @returns {Promise<object>} 包含处理后的内容部分和是否有公式的标志
   */
  private async processContent(errorInfo: string, textColor = ''): Promise<{
    contentParts: ContentPart[],
    hasFormula: boolean
  }> {
    // 使用静态方法处理文本内容（异步）
    const content = await WordAnnotationManager.processTextContent(errorInfo, this.logger.info);

    // 初始化内容部分
    const contentParts = this.initContentParts(content);

    // 处理HTML标签、表格和公式
    const processedWithHtml = await this.processHtml(contentParts, textColor);
    const processedWithTables = await this.processTables(processedWithHtml, textColor);
    return this.processLatex(processedWithTables, textColor);
  }

  /**
   * 静态方法：处理文本内容的字符串替换
   * @param content 需要处理的文本内容
   * @returns 处理后的文本内容
   */
  public static async processTextContent(content: string, logger): Promise<string> {
    let _content = content;
    _content = _content.replace(/\\\n/g, '\n');
    _content = _content.replace(/\$\$/g, '$ $');
    _content = _content.replace(/\\\\\$/g, '$$');
    _content = _content.replace(/\\\$/g, '$$');
    _content = _content.replace(/\n\s*\\\]/g, '$');
    // 公式需要转成 $$，但避免重复替换已经是$$的情况
    _content = _content.replace(/(?<!\$)\$(?!\$)/g, '$$$$');

    const formulas: string[] = [];
    const formulaPlaceholders: string[] = [];
    _content = _content.replace(/\$\$(.*?)\$\$/g, (_, p1) => {
      formulas.push(p1);
      const placeholder = `🤭${formulas.length - 1}🤭`;
      formulaPlaceholders.push(placeholder);
      return placeholder;
    });

    _content = _content.replace(/\\/g, '');
    _content = _content.replace(/🤭(\d+)🤭/g, (_, p1) => {
      return `$$${formulas[p1]}$$`;
    });

    // 处理公式中的特殊字符
    _content = _content.replace(/\$\$(.*?)\$\$/g, (_match, p1) => {
      return `$$${p1.replace(/</g, ' \\lt ').replace(/>/g, ' \\gt ')}$$`;
    });

    _content = _content.replace(/\\ce/g, '')
      .replace(/\n\s*\n+/g, '\n') // 将连续的换行符（中间可能有空格）转换为单个换行符
      .replace(/\\\\\\\\\\/g, '\\\\\\\\\\\\\\\\\\')
      .replace(/\\\\\\/g, '\\')
      .replace(/\\\\/g, '\\')
      .replace(/\\/g, '\\b')
      .replace(/\*\*/g, '')
      .replace(/ ight/g, ' \\right')
      .replace(/\\ight/g, ' \\right')
      .replace(/\\eq/g, ' \\neq');

    // 最后处理：对所有公式进行 KaTeX 验证和 AI 修复
    const finalFormulas: string[] = [];

    // 提取最终的公式
    _content = _content.replace(/\$\$(.*?)\$\$/g, (_, p1) => {
      finalFormulas.push(p1);
      return `🔥${finalFormulas.length - 1}🔥`;
    });

    // 处理和修复公式
    const fixedFormulas: string[] = [];
    for (let i = 0; i < finalFormulas.length; i++) {
      const formula = finalFormulas[i];

      try {
        // 尝试使用 KaTeX 解析公式
        katex.renderToString(formula, { throwOnError: true });
        fixedFormulas.push(formula);
      } catch (error) {
        logger(`KaTeX 解析失败，尝试使用大模型修复公式: ${formula}`);

        try {
          // 使用大模型修复公式
          const fixedFormula = await WordAnnotationManager.fixLatexWithAI(formula);

          // 再次验证修复后的公式
          try {
            katex.renderToString(fixedFormula, { throwOnError: true });
            fixedFormulas.push(fixedFormula);
          } catch (retryError) {
            logger(`大模型修复后的公式仍然无法解析，使用原公式: ${formula}`);
            fixedFormulas.push(formula);
          }
        } catch (aiError) {
          logger(`大模型修复失败，使用原公式: ${formula}`, typeof aiError === 'object' ? JSON.stringify(aiError) : aiError);
          fixedFormulas.push(formula);
        }
      }
    }

    // 替换回修复后的公式
    _content = _content.replace(/🔥(\d+)🔥/g, (_, p1) => {
      return `$$${fixedFormulas[parseInt(p1)]}$$`;
    });

    return _content;
  }

  /**
   * 使用AI修复LaTeX公式
   * @param formula 需要修复的公式
   * @returns 修复后的公式
   */
  private static async fixLatexWithAI(formula: string): Promise<string> {
    const openaiService = OpenAIService.getInstance();

    // 从URL获取prompt模板
    const promptResponse = await axios.get('https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/prompt/fix_latex_error.txt');
    const promptTemplate = promptResponse.data;

    // 替换模板中的占位符
    const prompt = promptTemplate.replace(':latex_str', formula);

    const messages = [
      {
        role: 'user',
        content: prompt,
      }
    ];
    const response = await openaiService.createChatCompletion(messages, { temperature: 0 });

    if (response.choices && response.choices.length > 0) {
      const content = response.choices[0].message.content.trim();

      // 提取修复后的公式（去除$$包装）
      const match = content.match(/\$\$(.*?)\$\$/);
      if (match) {
        return match[1];
      }

      // 如果没有$$包装，直接返回内容
      return content;
    }

    throw new Error('AI修复公式失败');
  }

  /**
   * 静态方法：处理agent_info生成HTML内容（异步版本）
   * @param node 包含agent_info的节点
   * @param options 配置选项
   * @returns 生成的HTML内容
   */
  public static async processAgentInfoToHtml(
    node: any,
    options: {
      checkDuplicatePrefix?: boolean;
      includeEmptyFallback?: boolean;
      logger?: (...args) => void
    } = {}
  ): Promise<string> {
    const { checkDuplicatePrefix = false, includeEmptyFallback = false, logger = console.log } = options;
    let htmlContent = '';

    if (node.agent_info) {
      // 先处理原始文本内容
      let processedAnswer = '';
      let processedAnalysis = '';

      // 处理答案文本
      if (node.agent_info.agent_answer) {
        let agent_answer = node.agent_info.agent_answer;
        agent_answer = await this.processTextContent(agent_answer, logger);
        processedAnswer = agent_answer;
      }

      // 处理解析文本
      if (node.agent_info.agent_analysis) {
        let agent_analysis = node.agent_info.agent_analysis;
        agent_analysis = await this.processTextContent(agent_analysis, logger);
        processedAnalysis = agent_analysis;
      }

      // 拼接HTML内容
      if (processedAnswer) {
        const answerLines = processedAnswer.split('\n');
        for (let i = 0; i < answerLines.length; i++) {
          if (i === 0) {
            // 如果启用了重复前缀检查，且内容已包含"AI编辑"，则不添加前缀
            if (checkDuplicatePrefix && answerLines[i].includes('AI编辑')) {
              htmlContent += `<p style="color: #0000FF;">${answerLines[i]}</p>`;
            } else {
              htmlContent += `<p style="color: #0000FF;"><b>【AI编辑：答案】</b>${answerLines[i]}</p>`;
            }
          } else {
            htmlContent += `<p style="color: #0000FF;">${answerLines[i]}</p>`;
          }
        }
      }

      if (processedAnalysis) {
        const analysisLines = processedAnalysis.split('\n');
        for (let i = 0; i < analysisLines.length; i++) {
          if (i === 0) {
            // 如果启用了重复前缀检查，且内容已包含"AI编辑"，则不添加前缀
            if (checkDuplicatePrefix && analysisLines[i].includes('AI编辑')) {
              htmlContent += `<p style="color: #0000FF;">${analysisLines[i]}</p>`;
            } else {
              htmlContent += `<p style="color: #0000FF;"><b>【AI编辑：解析】</b>${analysisLines[i]}</p>`;
            }
          } else {
            htmlContent += `<p style="color: #0000FF;">${analysisLines[i]}</p>`;
          }
        }
      }

      // 处理空内容的情况
      if (includeEmptyFallback && !processedAnswer && !processedAnalysis) {
        htmlContent += '<p style="color: #0000FF;"><b>【AI编辑】</b>暂无解析</p>';
      }

      // 最后将换行符转换为HTML段落
      if (htmlContent) {
        htmlContent = htmlContent.replace(/\n/g, '<p>&nbsp;</p>');
      }
    }

    return htmlContent;
  }

  /**
   * 统一空格格式，将标记的空格转换为普通空格用于比较
   * @param text 包含空格标记的文本
   * @returns 统一格式后的文本
   */
  private normalizeSpacesForComparison(text: string): string {
    return text
      .replace(/§SPACE§/g, ' ')
      .replace(/§TAB§/g, ' ')
      .replace(/§FULLSPACE§/g, ' ')
      .replace(/§NBSP§/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 将统一格式文本中的位置映射回原始文本的位置
   * @param originalText 原始文本（包含空格标记）
   * @param normalizedPosition 统一格式文本中的位置
   * @returns 原始文本中的对应位置
   */
  private mapNormalizedPositionToOriginal(originalText: string, normalizedPosition: number): number {
    let originalIndex = 0;
    let normalizedIndex = 0;

    while (originalIndex < originalText.length && normalizedIndex < normalizedPosition) {
      const char = originalText[originalIndex];

      if (char === '§') {
        // 检查是否是空格标记
        if (originalText.substring(originalIndex, originalIndex + 7) === '§SPACE§') {
          originalIndex += 7;
          normalizedIndex += 1;
        } else if (originalText.substring(originalIndex, originalIndex + 5) === '§TAB§') {
          originalIndex += 5;
          normalizedIndex += 1;
        } else if (originalText.substring(originalIndex, originalIndex + 11) === '§FULLSPACE§') {
          originalIndex += 11;
          normalizedIndex += 1;
        } else if (originalText.substring(originalIndex, originalIndex + 6) === '§NBSP§') {
          originalIndex += 6;
          normalizedIndex += 1;
        } else {
          originalIndex += 1;
          normalizedIndex += 1;
        }
      } else if (/\s/.test(char)) {
        // 跳过连续的空白字符，在统一格式中只对应一个空格
        while (originalIndex < originalText.length && /\s/.test(originalText[originalIndex])) {
          originalIndex++;
        }
        normalizedIndex += 1;
      } else {
        originalIndex += 1;
        normalizedIndex += 1;
      }
    }

    return originalIndex;
  }

  /**
   * 使用模糊匹配在文本中查找最佳匹配
   * @param fullText 完整文本（可能包含空格标记）
   * @param targetText 目标文字
   * @param threshold 相似度阈值 (0-100)
   * @returns 模糊匹配结果
   */
  private findFuzzyMatch(fullText: string, targetText: string, threshold: number = 80): {
    found: boolean;
    startIndex: number;
    endIndex: number;
    score: number;
    matchedText: string;
  } {
    const result = {
      found: false,
      startIndex: -1,
      endIndex: -1,
      score: 0,
      matchedText: '',
    };

    if (!fullText || !targetText || targetText.length === 0) {
      return result;
    }

    // 统一空格格式用于比较
    const normalizedFullText = this.normalizeSpacesForComparison(fullText);
    const normalizedTargetText = this.normalizeSpacesForComparison(targetText);

    // 边界优化：使用关键词的首尾字符来缩小搜索范围
    const firstChar = normalizedTargetText.charAt(0);
    const lastChar = normalizedTargetText.charAt(normalizedTargetText.length - 1);

    // 找到所有可能的起始位置（在统一格式的文本中查找首字符匹配）
    const possibleStarts: number[] = [];
    for (let i = 0; i < normalizedFullText.length; i++) {
      if (normalizedFullText.charAt(i) === firstChar) {
        possibleStarts.push(i);
      }
    }

    if (possibleStarts.length === 0) {
      return result;
    }

    let bestMatch = result;

    // 对每个可能的起始位置进行模糊匹配
    for (const startPos of possibleStarts) {
      // 在起始位置附近寻找合适长度的文本段
      const minLength = Math.max(1, normalizedTargetText.length - 5);
      const maxLength = normalizedTargetText.length + 10;

      for (let length = minLength; length <= maxLength && startPos + length <= normalizedFullText.length; length++) {
        const normalizedCandidate = normalizedFullText.substring(startPos, startPos + length);

        // 检查末尾字符是否匹配（进一步优化）
        if (normalizedCandidate.length >= normalizedTargetText.length && normalizedCandidate.charAt(normalizedCandidate.length - 1) !== lastChar) {
          continue;
        }

        // 计算相似度（使用统一格式的文本）
        const score = fuzzball.ratio(normalizedTargetText, normalizedCandidate);

        if (score >= threshold && score > bestMatch.score) {
          // 需要将统一格式的位置映射回原始文本的位置
          const originalStartIndex = this.mapNormalizedPositionToOriginal(fullText, startPos);
          const originalEndIndex = this.mapNormalizedPositionToOriginal(fullText, startPos + length);
          const originalCandidate = fullText.substring(originalStartIndex, originalEndIndex);

          bestMatch = {
            found: true,
            startIndex: originalStartIndex,
            endIndex: originalEndIndex,
            score,
            matchedText: originalCandidate,
          };
        }
      }
    }

    return bestMatch;
  }

  /**
   * 保持文本元素的属性，特别是xml:space="preserve"
   * @param originalTextElement 原始文本元素
   * @param newTextElement 新文本元素
   */
  private preserveTextElementAttributes(originalTextElement: Element, newTextElement: Element): void {
    if (originalTextElement.attributes && newTextElement.attributes) {
      // 保持xml:space属性
      if (originalTextElement.attributes['xml:space']) {
        newTextElement.attributes['xml:space'] = originalTextElement.attributes['xml:space'];
      }
      // 保持其他可能的属性
      Object.keys(originalTextElement.attributes).forEach(key => {
        if (!newTextElement.attributes![key]) {
          newTextElement.attributes![key] = originalTextElement.attributes![key];
        }
      });
    } else if (originalTextElement.attributes && !newTextElement.attributes) {
      // 如果新元素没有attributes但原始元素有，则复制所有属性
      newTextElement.attributes = { ...originalTextElement.attributes };
    }
  }

  /**
   * 深度克隆Word元素并保持文本属性
   * @param element 要克隆的元素
   * @returns 克隆的元素
   */
  private deepCloneWordElementWithAttributes(element: Element): Element {
    const cloned = this.deepCloneWordElement(element);

    // 确保文本元素的属性被正确保持
    const findAndPreserveTextElements = (original: Element, cloned: Element) => {
      if (original.name === 'w:t' && cloned.name === 'w:t') {
        this.preserveTextElementAttributes(original, cloned);
      }

      if (original.elements && cloned.elements) {
        for (let i = 0; i < Math.min(original.elements.length, cloned.elements.length); i++) {
          if (original.elements[i].type === 'element' && cloned.elements[i].type === 'element') {
            findAndPreserveTextElements(original.elements[i] as Element, cloned.elements[i] as Element);
          }
        }
      }
    };

    findAndPreserveTextElements(element, cloned);
    return cloned;
  }

  /**
   * 扩展匹配范围以包含前后的空格
   * @param fullText 完整文本
   * @param startIndex 关键词起始位置
   * @param endIndex 关键词结束位置
   * @returns 扩展后的匹配范围
   */
  private expandMatchWithSpaces(fullText: string, startIndex: number, endIndex: number): {
    expandedStart: number;
    expandedEnd: number;
  } {
    let expandedStart = startIndex;
    let expandedEnd = endIndex;

    // 向前扩展，包含前面的空格
    while (expandedStart > 0 && /\s/.test(fullText[expandedStart - 1])) {
      expandedStart--;
    }

    // 向后扩展，包含后面的空格
    while (expandedEnd < fullText.length && /\s/.test(fullText[expandedEnd])) {
      expandedEnd++;
    }

    return {
      expandedStart,
      expandedEnd
    };
  }

  /**
   * 在段落中查找目标文字，支持跨越多个w:r元素的情况，包含模糊匹配
   * @param paragraph 段落元素
   * @param targetText 目标文字
   * @returns 查找结果
   */
  private findTextInParagraph(paragraph: Element, targetText: string): {
    found: boolean;
    startRunIndex: number;
    endRunIndex: number;
    startTextIndex: number;
    endTextIndex: number;
    runs: Array<{
      run: Element;
      textElement: Element;
      text: string;
    }>;
  } {
    const result = {
      found: false,
      startRunIndex: -1,
      endRunIndex: -1,
      startTextIndex: -1,
      endTextIndex: -1,
      runs: [] as Array<{
        run: Element;
        textElement: Element;
        text: string;
      }>,
    };

    if (!paragraph.elements) {
      return result;
    }

    // 首先收集所有的文本运行和它们的文本内容
    const textRuns: Array<{
      run: Element;
      textElement: Element;
      text: string;
      runIndex: number;
    }> = [];

    for (let i = 0; i < paragraph.elements.length; i++) {
      const element = paragraph.elements[i];
      if (element.type === 'element' && element.name === 'w:r') {
        const textElement = element.elements?.find(
          (el) => el.type === 'element' && el.name === 'w:t'
        );
        if (textElement?.elements?.[0]?.text && typeof textElement.elements[0].text === 'string') {
          textRuns.push({
            run: element,
            textElement,
            text: textElement.elements[0].text,
            runIndex: i,
          });
        }
      }
    }

    if (textRuns.length === 0) {
      return result;
    }

    // 构建完整的段落文本
    const fullText = textRuns.map((run) => run.text).join('');

    // 先尝试精确匹配
    let targetIndex = fullText.indexOf(targetText);
    let targetEnd: number = -1;
    let usedFuzzyMatch = false;

    // 如果精确匹配失败，尝试空格规范化匹配
    if (targetIndex === -1) {
      // 规范化空格：将连续的空白字符替换为单个空格
      const normalizeSpaces = (text: string): string => {
        return text.replace(/\s+/g, ' ').trim();
      };

      const normalizedFullText = normalizeSpaces(fullText);
      const normalizedTargetText = normalizeSpaces(targetText);

      // 在规范化后的文本中查找
      const normalizedIndex = normalizedFullText.indexOf(normalizedTargetText);

      if (normalizedIndex !== -1) {
        // 需要将规范化后的索引映射回原始文本的索引
        // 这里使用一个简化的方法：逐字符对比找到对应位置
        let originalIndex = 0;
        let normalizedCounter = 0;

        while (originalIndex < fullText.length && normalizedCounter < normalizedIndex) {
          if (/\s/.test(fullText[originalIndex])) {
            // 跳过连续的空白字符，在规范化文本中只对应一个空格
            while (originalIndex < fullText.length && /\s/.test(fullText[originalIndex])) {
              originalIndex++;
            }
            normalizedCounter++;
          } else {
            originalIndex++;
            normalizedCounter++;
          }
        }

        targetIndex = originalIndex;
      } else {
        // 如果空格规范化匹配也失败，尝试模糊匹配
        const fuzzyResult = this.findFuzzyMatch(fullText, targetText, 80);
        if (fuzzyResult.found) {
          targetIndex = fuzzyResult.startIndex;
          targetEnd = fuzzyResult.endIndex;
          usedFuzzyMatch = true;
          // 记录模糊匹配的信息用于调试
          this.logger.info(`使用模糊匹配找到文本: "${targetText}" -> "${fuzzyResult.matchedText}" (相似度: ${fuzzyResult.score}%)`);
        }
      }
    }

    if (targetIndex === -1) {
      return result;
    }

    // 找到目标文字的起始和结束位置
    let targetStart = targetIndex;

    // 如果还没有设置targetEnd（即没有使用模糊匹配），则计算它
    if (!usedFuzzyMatch) {
      // 如果使用了规范化匹配，需要重新计算结束位置
      const normalizedFullText = fullText.replace(/\s+/g, ' ').trim();
      const normalizedTargetText = targetText.replace(/\s+/g, ' ').trim();

      if (normalizedFullText.indexOf(normalizedTargetText) !== -1 && fullText.indexOf(targetText) === -1) {
        // 使用了规范化匹配，需要计算原始文本中实际匹配部分的长度
        let originalIndex = targetStart;
        let normalizedCounter = 0;
        const normalizedTargetLength = normalizedTargetText.length;

        while (originalIndex < fullText.length && normalizedCounter < normalizedTargetLength) {
          if (/\s/.test(fullText[originalIndex])) {
            // 跳过连续的空白字符，在规范化文本中只对应一个字符
            while (originalIndex < fullText.length && /\s/.test(fullText[originalIndex])) {
              originalIndex++;
            }
            normalizedCounter++;
          } else {
            originalIndex++;
            normalizedCounter++;
          }
        }

        targetEnd = originalIndex;
      } else {
        // 精确匹配
        targetEnd = targetIndex + targetText.length;
      }
    }

    // 扩展匹配范围以包含前后的空格
    const expandedMatch = this.expandMatchWithSpaces(fullText, targetStart, targetEnd);
    targetStart = expandedMatch.expandedStart;
    targetEnd = expandedMatch.expandedEnd;

    // 映射回具体的运行和位置
    let currentPosition = 0;
    let startRunIndex = -1;
    let endRunIndex = -1;
    let startTextIndex = -1;
    let endTextIndex = -1;

    for (let i = 0; i < textRuns.length; i++) {
      const run = textRuns[i];
      const runStart = currentPosition;
      const runEnd = currentPosition + run.text.length;

      // 检查目标文字的开始位置
      if (startRunIndex === -1 && targetStart >= runStart && targetStart < runEnd) {
        startRunIndex = run.runIndex;
        startTextIndex = targetStart - runStart;
      }

      // 检查目标文字的结束位置
      if (targetEnd > runStart && targetEnd <= runEnd) {
        endRunIndex = run.runIndex;
        endTextIndex = targetEnd - runStart;
      }

      currentPosition = runEnd;
    }

    // 如果找到了开始和结束位置
    if (startRunIndex !== -1 && endRunIndex !== -1) {
      result.found = true;
      result.startRunIndex = startRunIndex;
      result.endRunIndex = endRunIndex;
      result.startTextIndex = startTextIndex;
      result.endTextIndex = endTextIndex;

      // 收集涉及的运行
      for (const run of textRuns) {
        if (run.runIndex >= startRunIndex && run.runIndex <= endRunIndex) {
          result.runs.push({
            run: run.run,
            textElement: run.textElement,
            text: run.text,
          });
        }
      }
    }

    return result;
  }

  /**
   * 为找到的文字添加注释标记
   * @param paragraph 段落元素
   * @param textSearchResult 文字查找结果
   * @param commentId 注释ID
   */
  private addCommentMarkersToFoundText(paragraph: Element, textSearchResult: any, commentId: string): void {
    if (!textSearchResult.found || !paragraph.elements) {
      return;
    }

    // 创建注释标记元素
    const commentStartRun: any = {
      type: 'element',
      name: 'w:commentRangeStart',
      attributes: { 'w:id': commentId },
    };

    const commentEndRun: any = {
      type: 'element',
      name: 'w:commentRangeEnd',
      attributes: { 'w:id': commentId },
    };

    const commentReference: any = {
      type: 'element',
      name: 'w:r',
      elements: [
        {
          type: 'element',
          name: 'w:rPr',
          elements: [
            {
              type: 'element',
              name: 'w:rStyle',
              attributes: { 'w:val': 'CommentReference' },
            }
          ],
        },
        {
          type: 'element',
          name: 'w:commentReference',
          attributes: { 'w:id': commentId },
        }
      ],
    };

    // 情况1：目标文字在同一个运行中
    if (textSearchResult.startRunIndex === textSearchResult.endRunIndex) {
      const targetRunIndex = textSearchResult.startRunIndex;
      const targetRun = paragraph.elements[targetRunIndex];
      const textElement = targetRun.elements?.find(
        (el) => el.type === 'element' && el.name === 'w:t'
      );

      if (textElement?.elements?.[0]?.text && typeof textElement.elements[0].text === 'string') {
        const originalText = textElement.elements[0].text;
        const beforeText = originalText.substring(0, textSearchResult.startTextIndex);
        const targetText = originalText.substring(textSearchResult.startTextIndex, textSearchResult.endTextIndex);
        const afterText = originalText.substring(textSearchResult.endTextIndex);

        const newElements: any = [];

        // 前置文本
        if (beforeText) {
          const beforeRun = this.deepCloneWordElementWithAttributes(targetRun);
          const beforeTextEl = beforeRun.elements?.find((el: any) => el.name === 'w:t');
          if (beforeTextEl && beforeTextEl.elements && beforeTextEl.elements[0]) {
            beforeTextEl.elements[0].text = beforeText;
          }
          newElements.push(beforeRun);
        }

        // 注释开始标记
        newElements.push(commentStartRun);

        // 目标文本
        const targetTextRun = this.deepCloneWordElementWithAttributes(targetRun);
        const targetTextEl = targetTextRun.elements?.find((el: any) => el.name === 'w:t');
        if (targetTextEl && targetTextEl.elements && targetTextEl.elements[0]) {
          targetTextEl.elements[0].text = targetText;
        }
        newElements.push(targetTextRun);

        // 注释结束标记和引用
        newElements.push(commentEndRun);
        newElements.push(commentReference);

        // 后置文本
        if (afterText) {
          const afterRun = this.deepCloneWordElementWithAttributes(targetRun);
          const afterTextEl = afterRun.elements?.find((el: any) => el.name === 'w:t');
          if (afterTextEl && afterTextEl.elements && afterTextEl.elements[0]) {
            afterTextEl.elements[0].text = afterText;
          }
          newElements.push(afterRun);
        }

        // 替换原始运行
        paragraph.elements.splice(targetRunIndex, 1, ...newElements);
      }
    } else {
      // 情况2：目标文字跨越多个运行
      const elementsToInsert: any = [];
      let insertionIndex = textSearchResult.startRunIndex;

      // 处理第一个运行
      const firstRun = paragraph.elements[textSearchResult.startRunIndex];
      const firstTextElement = firstRun.elements?.find(
        (el) => el.type === 'element' && el.name === 'w:t'
      );

      if (firstTextElement?.elements?.[0]?.text && typeof firstTextElement.elements[0].text === 'string') {
        const firstText = firstTextElement.elements[0].text;
        const beforeText = firstText.substring(0, textSearchResult.startTextIndex);
        const targetText = firstText.substring(textSearchResult.startTextIndex);

        if (beforeText) {
          const beforeRun = this.deepCloneWordElementWithAttributes(firstRun);
          const beforeTextEl = beforeRun.elements?.find((el: any) => el.name === 'w:t');
          if (beforeTextEl && beforeTextEl.elements && beforeTextEl.elements[0]) {
            beforeTextEl.elements[0].text = beforeText;
          }
          elementsToInsert.push(beforeRun);
        }

        // 注释开始标记
        elementsToInsert.push(commentStartRun);

        // 第一个运行的目标文本部分
        const firstTargetRun = this.deepCloneWordElementWithAttributes(firstRun);
        const firstTargetTextEl = firstTargetRun.elements?.find((el: any) => el.name === 'w:t');
        if (firstTargetTextEl && firstTargetTextEl.elements && firstTargetTextEl.elements[0]) {
          firstTargetTextEl.elements[0].text = targetText;
        }
        elementsToInsert.push(firstTargetRun);
      }

      // 处理中间的运行（如果有）
      for (let i = textSearchResult.startRunIndex + 1; i < textSearchResult.endRunIndex; i++) {
        elementsToInsert.push(paragraph.elements[i]);
      }

      // 处理最后一个运行
      const lastRun = paragraph.elements[textSearchResult.endRunIndex];
      const lastTextElement = lastRun.elements?.find(
        (el) => el.type === 'element' && el.name === 'w:t'
      );

      if (lastTextElement?.elements?.[0]?.text && typeof lastTextElement.elements[0].text === 'string') {
        const lastText = lastTextElement.elements[0].text;
        const targetText = lastText.substring(0, textSearchResult.endTextIndex);
        const afterText = lastText.substring(textSearchResult.endTextIndex);

        // 最后一个运行的目标文本部分
        const lastTargetRun = this.deepCloneWordElementWithAttributes(lastRun);
        const lastTargetTextEl = lastTargetRun.elements?.find((el: any) => el.name === 'w:t');
        if (lastTargetTextEl && lastTargetTextEl.elements && lastTargetTextEl.elements[0]) {
          lastTargetTextEl.elements[0].text = targetText;
        }
        elementsToInsert.push(lastTargetRun);

        // 注释结束标记和引用
        elementsToInsert.push(commentEndRun);
        elementsToInsert.push(commentReference);

        // 后置文本
        if (afterText) {
          const afterRun = this.deepCloneWordElementWithAttributes(lastRun);
          const afterTextEl = afterRun.elements?.find((el: any) => el.name === 'w:t');
          if (afterTextEl && afterTextEl.elements && afterTextEl.elements[0]) {
            afterTextEl.elements[0].text = afterText;
          }
          elementsToInsert.push(afterRun);
        }
      }

      // 替换所有涉及的运行
      const deleteCount = textSearchResult.endRunIndex - textSearchResult.startRunIndex + 1;
      paragraph.elements.splice(insertionIndex, deleteCount, ...elementsToInsert);
    }
  }
}

export default WordAnnotationManager;
